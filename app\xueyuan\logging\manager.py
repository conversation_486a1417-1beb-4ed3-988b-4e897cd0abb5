# coding:utf-8
"""
日志管理器

该模块提供日志管理功能，包括日志记录、
日志级别控制、日志文件管理等。

主要功能：
- 多级别日志记录
- 日志文件轮转
- 日志格式化
- 日志过滤

类说明：
- LogManager: 日志管理器类
"""

import os
import logging
import threading
from typing import Optional, Dict, Any, List, Callable
from datetime import datetime, timedelta
from logging.handlers import RotatingFileHandler, TimedRotatingFileHandler
from pathlib import Path

from ..common.config_loader import get_study_config
from ..database.dao import DAOFactory


class LogManager:
    """
    日志管理器类
    
    提供统一的日志记录和管理功能
    """
    
    def __init__(self):
        """初始化日志管理器"""
        # 配置
        cfg = get_study_config()
        self.log_level = cfg.get(cfg.logLevel, "INFO")
        self.log_dir = cfg.get(cfg.logDir, "logs")
        self.max_file_size = cfg.get(cfg.maxLogFileSize, 10 * 1024 * 1024)  # 10MB
        self.backup_count = cfg.get(cfg.logBackupCount, 5)
        self.enable_console = cfg.get(cfg.enableConsoleLog, True)
        self.enable_file = cfg.get(cfg.enableFileLog, True)
        self.enable_database = cfg.get(cfg.enableDatabaseLog, True)
        
        # 创建日志目录
        self.log_dir_path = Path(self.log_dir)
        self.log_dir_path.mkdir(parents=True, exist_ok=True)
        
        # 日志记录器
        self.logger = logging.getLogger("xueyuan")
        self.logger.setLevel(getattr(logging, self.log_level.upper()))
        
        # 防止重复添加处理器
        if not self.logger.handlers:
            self._setup_handlers()
        
        # 数据库DAO
        self.log_dao = DAOFactory.get_log_dao() if self.enable_database else None
        
        # 日志缓存
        self.log_cache: List[Dict[str, Any]] = []
        self.cache_lock = threading.Lock()
        self.max_cache_size = 1000
        
        # 日志监听器
        self.listeners: List[Callable] = []
        
        print(f"[日志管理] 日志管理器初始化完成，级别: {self.log_level}")
    
    def _setup_handlers(self):
        """设置日志处理器"""
        # 日志格式
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(module)s:%(lineno)d - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # 控制台处理器
        if self.enable_console:
            console_handler = logging.StreamHandler()
            console_handler.setLevel(getattr(logging, self.log_level.upper()))
            console_handler.setFormatter(formatter)
            self.logger.addHandler(console_handler)
        
        # 文件处理器
        if self.enable_file:
            # 主日志文件
            main_log_file = self.log_dir_path / "xueyuan.log"
            file_handler = RotatingFileHandler(
                main_log_file,
                maxBytes=self.max_file_size,
                backupCount=self.backup_count,
                encoding='utf-8'
            )
            file_handler.setLevel(getattr(logging, self.log_level.upper()))
            file_handler.setFormatter(formatter)
            self.logger.addHandler(file_handler)
            
            # 错误日志文件
            error_log_file = self.log_dir_path / "error.log"
            error_handler = RotatingFileHandler(
                error_log_file,
                maxBytes=self.max_file_size,
                backupCount=self.backup_count,
                encoding='utf-8'
            )
            error_handler.setLevel(logging.ERROR)
            error_handler.setFormatter(formatter)
            self.logger.addHandler(error_handler)
        
        print("[日志管理] 日志处理器设置完成")
    
    def debug(self, message: str, user_phone: str = "system", module: str = "unknown", **kwargs):
        """
        记录调试日志
        
        Args:
            message: 日志消息
            user_phone: 用户手机号
            module: 模块名称
            **kwargs: 额外参数
        """
        self._log("DEBUG", message, user_phone, module, **kwargs)
    
    def info(self, message: str, user_phone: str = "system", module: str = "unknown", **kwargs):
        """
        记录信息日志
        
        Args:
            message: 日志消息
            user_phone: 用户手机号
            module: 模块名称
            **kwargs: 额外参数
        """
        self._log("INFO", message, user_phone, module, **kwargs)
    
    def warning(self, message: str, user_phone: str = "system", module: str = "unknown", **kwargs):
        """
        记录警告日志
        
        Args:
            message: 日志消息
            user_phone: 用户手机号
            module: 模块名称
            **kwargs: 额外参数
        """
        self._log("WARNING", message, user_phone, module, **kwargs)
    
    def error(self, message: str, user_phone: str = "system", module: str = "unknown", **kwargs):
        """
        记录错误日志
        
        Args:
            message: 日志消息
            user_phone: 用户手机号
            module: 模块名称
            **kwargs: 额外参数
        """
        self._log("ERROR", message, user_phone, module, **kwargs)
    
    def critical(self, message: str, user_phone: str = "system", module: str = "unknown", **kwargs):
        """
        记录严重错误日志
        
        Args:
            message: 日志消息
            user_phone: 用户手机号
            module: 模块名称
            **kwargs: 额外参数
        """
        self._log("CRITICAL", message, user_phone, module, **kwargs)
    
    def _log(self, level: str, message: str, user_phone: str, module: str, **kwargs):
        """
        内部日志记录方法
        
        Args:
            level: 日志级别
            message: 日志消息
            user_phone: 用户手机号
            module: 模块名称
            **kwargs: 额外参数
        """
        try:
            timestamp = datetime.now()
            
            # 创建日志记录
            log_record = {
                'timestamp': timestamp,
                'level': level,
                'message': message,
                'user_phone': user_phone,
                'module': module,
                **kwargs
            }
            
            # 记录到标准日志
            log_level = getattr(logging, level.upper())
            self.logger.log(log_level, f"[{user_phone}][{module}] {message}")
            
            # 添加到缓存
            with self.cache_lock:
                self.log_cache.append(log_record)
                
                # 限制缓存大小
                if len(self.log_cache) > self.max_cache_size:
                    self.log_cache.pop(0)
            
            # 保存到数据库
            if self.enable_database and self.log_dao:
                try:
                    db_log_data = {
                        'user_phone': user_phone,
                        'level': level,
                        'message': message,
                        'module': module,
                        'timestamp': timestamp
                    }
                    self.log_dao.create(db_log_data)
                except Exception as e:
                    # 避免日志记录本身出错导致的循环
                    print(f"[日志管理] 保存日志到数据库失败: {e}")
            
            # 通知监听器
            self._notify_listeners(log_record)
            
        except Exception as e:
            # 避免日志记录本身出错
            print(f"[日志管理] 记录日志失败: {e}")
    
    def _notify_listeners(self, log_record: Dict[str, Any]):
        """
        通知日志监听器
        
        Args:
            log_record: 日志记录
        """
        for listener in self.listeners:
            try:
                listener(log_record)
            except Exception as e:
                print(f"[日志管理] 日志监听器执行失败: {e}")
    
    def add_listener(self, listener: Callable):
        """
        添加日志监听器
        
        Args:
            listener: 监听器函数
        """
        self.listeners.append(listener)
        print(f"[日志管理] 添加日志监听器: {listener.__name__}")
    
    def remove_listener(self, listener: Callable):
        """
        移除日志监听器
        
        Args:
            listener: 监听器函数
        """
        if listener in self.listeners:
            self.listeners.remove(listener)
            print(f"[日志管理] 移除日志监听器: {listener.__name__}")
    
    def get_logs(self, level: Optional[str] = None, user_phone: Optional[str] = None,
                module: Optional[str] = None, start_time: Optional[datetime] = None,
                end_time: Optional[datetime] = None, limit: int = 100,
                source: str = "cache") -> List[Dict[str, Any]]:
        """
        获取日志记录
        
        Args:
            level: 日志级别过滤
            user_phone: 用户手机号过滤
            module: 模块名称过滤
            start_time: 开始时间
            end_time: 结束时间
            limit: 返回数量限制
            source: 数据源（"cache" 或 "database"）
            
        Returns:
            List[Dict[str, Any]]: 日志记录列表
        """
        try:
            if source == "cache":
                return self._get_logs_from_cache(level, user_phone, module, start_time, end_time, limit)
            elif source == "database" and self.log_dao:
                return self._get_logs_from_database(level, user_phone, module, start_time, end_time, limit)
            else:
                return []
                
        except Exception as e:
            print(f"[日志管理] 获取日志失败: {e}")
            return []
    
    def _get_logs_from_cache(self, level: Optional[str], user_phone: Optional[str],
                           module: Optional[str], start_time: Optional[datetime],
                           end_time: Optional[datetime], limit: int) -> List[Dict[str, Any]]:
        """从缓存获取日志"""
        with self.cache_lock:
            filtered_logs = []
            
            for log_record in reversed(self.log_cache):  # 最新的在前
                # 应用过滤条件
                if level and log_record.get('level') != level:
                    continue
                
                if user_phone and log_record.get('user_phone') != user_phone:
                    continue
                
                if module and log_record.get('module') != module:
                    continue
                
                if start_time and log_record.get('timestamp', datetime.min) < start_time:
                    continue
                
                if end_time and log_record.get('timestamp', datetime.max) > end_time:
                    continue
                
                filtered_logs.append(log_record.copy())
                
                # 限制数量
                if len(filtered_logs) >= limit:
                    break
            
            return filtered_logs
    
    def _get_logs_from_database(self, level: Optional[str], user_phone: Optional[str],
                              module: Optional[str], start_time: Optional[datetime],
                              end_time: Optional[datetime], limit: int) -> List[Dict[str, Any]]:
        """从数据库获取日志"""
        try:
            # 构建查询条件
            conditions = {}
            if level:
                conditions['level'] = level
            if user_phone:
                conditions['user_phone'] = user_phone
            if module:
                conditions['module'] = module
            if start_time:
                conditions['start_time'] = start_time
            if end_time:
                conditions['end_time'] = end_time
            
            # 查询数据库
            logs = self.log_dao.get_logs(conditions, limit)
            return logs or []
            
        except Exception as e:
            print(f"[日志管理] 从数据库获取日志失败: {e}")
            return []
    
    def clear_logs(self, older_than: Optional[datetime] = None, level: Optional[str] = None):
        """
        清理日志
        
        Args:
            older_than: 清理指定时间之前的日志
            level: 清理指定级别的日志
        """
        try:
            # 清理缓存
            with self.cache_lock:
                if older_than:
                    self.log_cache = [
                        log for log in self.log_cache
                        if log.get('timestamp', datetime.max) >= older_than
                    ]
                
                if level:
                    self.log_cache = [
                        log for log in self.log_cache
                        if log.get('level') != level
                    ]
            
            # 清理数据库
            if self.log_dao:
                conditions = {}
                if older_than:
                    conditions['older_than'] = older_than
                if level:
                    conditions['level'] = level
                
                self.log_dao.delete_logs(conditions)
            
            print(f"[日志管理] 日志清理完成")
            
        except Exception as e:
            print(f"[日志管理] 清理日志失败: {e}")
    
    def get_log_stats(self) -> Dict[str, Any]:
        """
        获取日志统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            with self.cache_lock:
                cache_stats = {
                    'total_count': len(self.log_cache),
                    'level_counts': {},
                    'module_counts': {},
                    'user_counts': {}
                }
                
                for log_record in self.log_cache:
                    level = log_record.get('level', 'UNKNOWN')
                    module = log_record.get('module', 'unknown')
                    user_phone = log_record.get('user_phone', 'unknown')
                    
                    cache_stats['level_counts'][level] = cache_stats['level_counts'].get(level, 0) + 1
                    cache_stats['module_counts'][module] = cache_stats['module_counts'].get(module, 0) + 1
                    cache_stats['user_counts'][user_phone] = cache_stats['user_counts'].get(user_phone, 0) + 1
            
            # 数据库统计
            db_stats = {}
            if self.log_dao:
                try:
                    db_stats = self.log_dao.get_stats()
                except Exception as e:
                    print(f"[日志管理] 获取数据库统计失败: {e}")
            
            return {
                'cache': cache_stats,
                'database': db_stats,
                'config': {
                    'log_level': self.log_level,
                    'enable_console': self.enable_console,
                    'enable_file': self.enable_file,
                    'enable_database': self.enable_database,
                    'max_cache_size': self.max_cache_size
                }
            }
            
        except Exception as e:
            print(f"[日志管理] 获取日志统计失败: {e}")
            return {}
    
    def set_log_level(self, level: str):
        """
        设置日志级别
        
        Args:
            level: 日志级别
        """
        try:
            self.log_level = level.upper()
            self.logger.setLevel(getattr(logging, self.log_level))
            
            # 更新所有处理器的级别
            for handler in self.logger.handlers:
                if not isinstance(handler, logging.handlers.RotatingFileHandler) or "error.log" not in str(handler.baseFilename):
                    handler.setLevel(getattr(logging, self.log_level))
            
            print(f"[日志管理] 日志级别已设置为: {self.log_level}")
            
        except Exception as e:
            print(f"[日志管理] 设置日志级别失败: {e}")
    
    def get_log_files(self) -> List[Dict[str, Any]]:
        """
        获取日志文件列表
        
        Returns:
            List[Dict[str, Any]]: 日志文件信息
        """
        try:
            log_files = []
            
            for file_path in self.log_dir_path.glob("*.log*"):
                if file_path.is_file():
                    stat = file_path.stat()
                    log_files.append({
                        'name': file_path.name,
                        'path': str(file_path),
                        'size': stat.st_size,
                        'modified_time': datetime.fromtimestamp(stat.st_mtime)
                    })
            
            # 按修改时间排序
            log_files.sort(key=lambda x: x['modified_time'], reverse=True)
            
            return log_files
            
        except Exception as e:
            print(f"[日志管理] 获取日志文件列表失败: {e}")
            return []


# 全局日志管理器实例
log_manager = LogManager()
