# coding:utf-8
"""
数据库初始化脚本

该模块提供数据库初始化功能，包括：
- 创建数据库文件
- 创建表结构
- 创建索引
- 初始化基础数据

主要功能：
- init_database(): 初始化数据库
- create_sample_data(): 创建示例数据
- check_database_health(): 检查数据库健康状态
"""

from pathlib import Path
from datetime import datetime

from .manager import db_manager
from .dao import DAOFactory
from .models import User, Log
from ..common.constants import UserStatus, LogLevel


def init_database() -> bool:
    """
    初始化数据库
    
    Returns:
        bool: 初始化是否成功
    """
    try:
        print("[数据库] 开始初始化数据库...")
        
        # 确保数据库目录存在
        db_path = Path(db_manager.db_path)
        db_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 初始化数据库表结构
        if not db_manager.initialize_database():
            print("[数据库] 初始化数据库表结构失败")
            return False
        
        # 创建初始日志记录
        log_dao = DAOFactory.get_log_dao()
        init_log = Log(
            level=LogLevel.INFO,
            message="数据库初始化完成",
            module="database.init_db",
            user_phone=None
        )
        log_dao.create(init_log)
        
        print("[数据库] 数据库初始化成功")
        return True
        
    except Exception as e:
        print(f"[数据库] 初始化数据库失败: {e}")
        return False


def create_sample_data() -> bool:
    """
    创建示例数据
    
    Returns:
        bool: 创建是否成功
    """
    try:
        print("[数据库] 开始创建示例数据...")
        
        user_dao = DAOFactory.get_user_dao()
        log_dao = DAOFactory.get_log_dao()
        
        # 创建示例用户
        sample_users = [
            User(
                phone="18900000001",
                password="password123",
                name="测试用户1",
                status=UserStatus.NOT_STARTED
            ),
            User(
                phone="18900000002",
                password="password123",
                name="测试用户2",
                status=UserStatus.NOT_STARTED
            )
        ]
        
        for user in sample_users:
            if not user_dao.get_by_phone(user.phone):  # 避免重复创建
                user_dao.create(user)
                print(f"[数据库] 已创建示例用户: {user.phone}")
        
        # 创建示例日志
        sample_log = Log(
            level=LogLevel.INFO,
            message="示例数据创建完成",
            module="database.init_db",
            user_phone=None
        )
        log_dao.create(sample_log)
        
        print("[数据库] 示例数据创建成功")
        return True
        
    except Exception as e:
        print(f"[数据库] 创建示例数据失败: {e}")
        return False


def check_database_health() -> dict:
    """
    检查数据库健康状态
    
    Returns:
        dict: 数据库健康状态信息
    """
    try:
        health_info = {
            "status": "healthy",
            "database_exists": False,
            "tables_exist": False,
            "connection_ok": False,
            "table_counts": {},
            "errors": []
        }
        
        # 检查数据库文件是否存在
        if db_manager.check_database_exists():
            health_info["database_exists"] = True
        else:
            health_info["errors"].append("数据库文件不存在")
        
        # 检查数据库连接
        try:
            with db_manager.get_connection() as conn:
                health_info["connection_ok"] = True
                
                # 检查表是否存在
                tables = conn.execute(
                    "SELECT name FROM sqlite_master WHERE type='table'"
                ).fetchall()
                
                expected_tables = ["users", "courses", "logs", "api_data"]
                existing_tables = [table["name"] for table in tables]
                
                if all(table in existing_tables for table in expected_tables):
                    health_info["tables_exist"] = True
                    
                    # 获取各表记录数
                    for table in expected_tables:
                        count = conn.execute(f"SELECT COUNT(*) as count FROM {table}").fetchone()
                        health_info["table_counts"][table] = count["count"]
                else:
                    missing_tables = [table for table in expected_tables if table not in existing_tables]
                    health_info["errors"].append(f"缺少表: {missing_tables}")
                    
        except Exception as e:
            health_info["connection_ok"] = False
            health_info["errors"].append(f"数据库连接失败: {e}")
        
        # 判断整体健康状态
        if health_info["errors"]:
            health_info["status"] = "unhealthy"
        elif not all([health_info["database_exists"], health_info["tables_exist"], health_info["connection_ok"]]):
            health_info["status"] = "warning"
        
        return health_info
        
    except Exception as e:
        return {
            "status": "error",
            "database_exists": False,
            "tables_exist": False,
            "connection_ok": False,
            "table_counts": {},
            "errors": [f"健康检查失败: {e}"]
        }


def reset_database() -> bool:
    """
    重置数据库（删除所有数据）
    
    Returns:
        bool: 重置是否成功
    """
    try:
        print("[数据库] 开始重置数据库...")
        
        # 备份当前数据库
        if db_manager.check_database_exists():
            db_manager.backup_database()
        
        # 删除数据库文件
        db_path = Path(db_manager.db_path)
        if db_path.exists():
            db_path.unlink()
            print("[数据库] 已删除旧数据库文件")
        
        # 重新初始化
        db_manager._initialized = False
        if init_database():
            print("[数据库] 数据库重置成功")
            return True
        else:
            print("[数据库] 数据库重置失败")
            return False
            
    except Exception as e:
        print(f"[数据库] 重置数据库失败: {e}")
        return False


def main():
    """主函数，用于命令行执行"""
    import sys
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "init":
            success = init_database()
            sys.exit(0 if success else 1)
        elif command == "sample":
            success = create_sample_data()
            sys.exit(0 if success else 1)
        elif command == "health":
            health = check_database_health()
            print(f"数据库健康状态: {health}")
            sys.exit(0 if health["status"] == "healthy" else 1)
        elif command == "reset":
            success = reset_database()
            sys.exit(0 if success else 1)
        else:
            print("未知命令，支持的命令: init, sample, health, reset")
            sys.exit(1)
    else:
        # 默认执行初始化
        success = init_database()
        sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
