# coding:utf-8
"""
学习工具主界面

该模块定义了学习工具的主界面类 StudyMainInterface，
使用Pivot导航结构管理各个子界面。

主要功能：
- 创建和管理各个子界面（学习控制、用户管理、进度监控等）
- 配置Pivot导航栏和导航项
- 处理界面切换和事件响应
- 集成学习工具配置和数据库

类说明：
- StudyMainInterface: 学习工具主界面类，提供完整的界面管理功能
"""

from PySide6.QtCore import Qt, QTimer
from PySide6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout
from qfluentwidgets import (ScrollArea, Pivot, qrouter, SegmentedWidget, 
                            InfoBar, InfoBarPosition, FluentIcon as FIF)

from .study_control_interface import StudyControlInterface
from .user_manage_interface import UserManageInterface
from .progress_monitor_interface import ProgressMonitorInterface
from .log_view_interface import LogViewInterface
from .study_setting_interface import StudySettingInterface
from ..database.init_db import init_database, check_database_health


class StudyMainInterface(ScrollArea):
    """
    学习工具主界面类

    继承自 ScrollArea，提供学习工具的主界面功能。
    使用Pivot导航结构管理各个子界面，包括学习控制、用户管理、
    进度监控、日志查看和学习设置等功能模块。
    """

    def __init__(self, parent=None):
        """
        初始化学习工具主界面

        创建主界面实例，初始化各个子界面，配置Pivot导航栏，
        并设置界面布局和样式。
        """
        super().__init__(parent)
        self.setObjectName("StudyMainInterface")
        
        # 创建主容器
        self.view = QWidget()
        self.vBoxLayout = QVBoxLayout(self.view)
        
        # 创建Pivot导航
        self.pivot = Pivot(self)
        
        # 创建堆叠容器
        self.stackedWidget = SegmentedWidget(self)
        
        # 初始化界面
        self.initWidget()
        self.initLayout()
        self.connectSignalToSlot()
        
        # 初始化数据库
        self.initDatabase()

    def initWidget(self):
        """初始化界面组件"""
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.setViewportMargins(0, 80, 0, 20)
        self.setWidget(self.view)
        self.setWidgetResizable(True)
        
        # 创建子界面
        self.studyControlInterface = StudyControlInterface(self)
        self.userManageInterface = UserManageInterface(self)
        self.progressMonitorInterface = ProgressMonitorInterface(self)
        self.logViewInterface = LogViewInterface(self)
        self.studySettingInterface = StudySettingInterface(self)
        
        # 添加子界面到Pivot
        self.addSubInterface(self.studyControlInterface, 'studyControl', self.tr('学习控制'), FIF.PLAY)
        self.addSubInterface(self.userManageInterface, 'userManage', self.tr('用户管理'), FIF.PEOPLE)
        self.addSubInterface(self.progressMonitorInterface, 'progressMonitor', self.tr('进度监控'), FIF.CHART)
        self.addSubInterface(self.logViewInterface, 'logView', self.tr('日志查看'), FIF.DOCUMENT)
        self.addSubInterface(self.studySettingInterface, 'studySetting', self.tr('学习设置'), FIF.SETTING)
        
        # 设置默认界面
        self.stackedWidget.setCurrentWidget(self.studyControlInterface)
        self.pivot.setCurrentItem(self.studyControlInterface.objectName())

    def initLayout(self):
        """初始化布局"""
        self.vBoxLayout.setContentsMargins(36, 0, 36, 0)
        self.vBoxLayout.setSpacing(40)
        self.vBoxLayout.addWidget(self.pivot, 0, Qt.AlignmentFlag.AlignHCenter)
        self.vBoxLayout.addWidget(self.stackedWidget)

    def addSubInterface(self, widget: QWidget, objectName: str, text: str, icon=None):
        """
        添加子界面
        
        Args:
            widget: 子界面组件
            objectName: 对象名称
            text: 显示文本
            icon: 图标
        """
        widget.setObjectName(objectName)
        self.stackedWidget.addWidget(widget)
        
        # 添加到Pivot导航
        if icon:
            self.pivot.addItem(
                routeKey=objectName,
                text=text,
                onClick=lambda: self.stackedWidget.setCurrentWidget(widget),
                icon=icon
            )
        else:
            self.pivot.addItem(
                routeKey=objectName,
                text=text,
                onClick=lambda: self.stackedWidget.setCurrentWidget(widget)
            )

    def connectSignalToSlot(self):
        """连接信号到槽函数"""
        # 连接Pivot导航信号
        self.pivot.currentItemChanged.connect(self.onCurrentItemChanged)
        
        # 连接子界面信号（如果需要）
        # self.studyControlInterface.someSignal.connect(self.onSomeSlot)

    def onCurrentItemChanged(self, objectName: str):
        """
        当前导航项改变时的处理
        
        Args:
            objectName: 当前选中的界面对象名称
        """
        widget = self.findChild(QWidget, objectName)
        if widget:
            self.stackedWidget.setCurrentWidget(widget)

    def initDatabase(self):
        """初始化数据库"""
        try:
            # 检查数据库健康状态
            health = check_database_health()
            
            if health["status"] == "healthy":
                self.showInfoBar("数据库连接正常", "success")
            elif health["status"] == "warning":
                self.showInfoBar("数据库需要初始化", "warning")
                # 尝试初始化数据库
                if init_database():
                    self.showInfoBar("数据库初始化成功", "success")
                else:
                    self.showInfoBar("数据库初始化失败", "error")
            else:
                self.showInfoBar("数据库连接失败", "error")
                
        except Exception as e:
            self.showInfoBar(f"数据库初始化异常: {str(e)}", "error")

    def showInfoBar(self, message: str, type: str = "info"):
        """
        显示信息栏
        
        Args:
            message: 消息内容
            type: 消息类型 (info, success, warning, error)
        """
        if type == "success":
            InfoBar.success(
                title=self.tr("成功"),
                content=message,
                orient=Qt.Orientation.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP,
                duration=3000,
                parent=self
            )
        elif type == "warning":
            InfoBar.warning(
                title=self.tr("警告"),
                content=message,
                orient=Qt.Orientation.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP,
                duration=5000,
                parent=self
            )
        elif type == "error":
            InfoBar.error(
                title=self.tr("错误"),
                content=message,
                orient=Qt.Orientation.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP,
                duration=8000,
                parent=self
            )
        else:
            InfoBar.info(
                title=self.tr("信息"),
                content=message,
                orient=Qt.Orientation.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP,
                duration=3000,
                parent=self
            )

    def tr(self, text: str) -> str:
        """翻译文本（暂时直接返回）"""
        return text
