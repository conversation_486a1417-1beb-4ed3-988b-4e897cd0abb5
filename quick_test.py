#!/usr/bin/env python
# coding:utf-8
"""
快速测试脚本，验证核心功能是否正常
"""

def test_imports():
    """测试基本导入"""
    print("测试基本导入...")
    
    try:
        import sys
        print(f"✅ Python版本: {sys.version_info}")
        
        import sqlite3
        print("✅ sqlite3")
        
        import json
        print("✅ json")
        
        import threading
        print("✅ threading")
        
        return True
        
    except Exception as e:
        print(f"❌ 基本导入失败: {e}")
        return False


def test_database():
    """测试数据库"""
    print("\n测试数据库...")
    
    try:
        import sqlite3
        import tempfile
        import os
        
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as f:
            db_path = f.name
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE users (
                id INTEGER PRIMARY KEY,
                phone TEXT UNIQUE NOT NULL,
                name TEXT NOT NULL
            )
        ''')
        
        cursor.execute("INSERT INTO users (phone, name) VALUES (?, ?)", 
                      ("13800000001", "测试用户"))
        conn.commit()
        
        cursor.execute("SELECT * FROM users WHERE phone = ?", ("13800000001",))
        result = cursor.fetchone()
        
        conn.close()
        os.unlink(db_path)
        
        if result and result[2] == "测试用户":
            print("✅ 数据库操作正常")
            return True
        else:
            print("❌ 数据库查询失败")
            return False
            
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
        return False


def test_ddddocr_import():
    """测试ddddocr导入"""
    print("\n测试ddddocr导入...")
    
    try:
        import ddddocr
        print("✅ ddddocr导入成功")
        return True
        
    except Exception as e:
        print(f"❌ ddddocr导入失败: {e}")
        return False


def test_project_structure():
    """测试项目结构"""
    print("\n测试项目结构...")
    
    try:
        import os
        
        required_dirs = [
            "app",
            "app/xueyuan", 
            "app/ocr",
            "data",
            "tests",
            "docs"
        ]
        
        missing_dirs = []
        for dir_path in required_dirs:
            if not os.path.exists(dir_path):
                missing_dirs.append(dir_path)
        
        if missing_dirs:
            print(f"❌ 缺少目录: {missing_dirs}")
            return False
        else:
            print("✅ 项目结构完整")
            return True
            
    except Exception as e:
        print(f"❌ 项目结构检查失败: {e}")
        return False


def main():
    """主函数"""
    print("=" * 50)
    print("学习工具快速测试")
    print("=" * 50)
    
    tests = [
        ("基本导入", test_imports),
        ("数据库功能", test_database),
        ("ddddocr导入", test_ddddocr_import),
        ("项目结构", test_project_structure)
    ]
    
    passed = 0
    total = len(tests)
    
    for name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {name} 测试失败")
        except Exception as e:
            print(f"❌ {name} 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 快速测试全部通过！")
        print("\n✅ 基础环境配置正确")
        print("✅ ddddocr 1.6.0 已正确安装")
        print("✅ 项目结构完整")
        print("✅ 数据库功能正常")
        return True
    else:
        print("💥 部分测试失败")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
