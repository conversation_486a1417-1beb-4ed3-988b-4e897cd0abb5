# coding:utf-8
"""
学习工具主窗口

该模块提供学习工具的主界面窗口，基于MSFluentWindow实现，
包含Pivot导航、状态栏、工具栏等界面元素。

主要功能：
- Pivot导航界面
- 多页面管理
- 状态栏显示
- 工具栏操作
- 主题切换

类说明：
- MainWindow: 主窗口类
"""

from PySide6.QtWidgets import QApplication, QHBoxLayout, QVBoxLayout, QWidget
from PySide6.QtCore import Qt, QTimer, QSize
from PySide6.QtGui import QIcon, QPixmap
from qfluentwidgets import (MSFluentWindow, NavigationItemPosition, FluentIcon as FIF,
                           InfoBar, InfoBarPosition, setTheme, Theme, isDarkTheme,
                           PushButton, ToolButton, Action, RoundMenu, MenuAnimationType,
                           Pivot, qrouter, SubtitleLabel,
                           setFont, BodyLabel, CaptionLabel, StrongBodyLabel)
try:
    from qfluentwidgets import SystemTrayIcon, SystemTrayMenu
    SYSTEM_TRAY_AVAILABLE = True
except ImportError:
    SYSTEM_TRAY_AVAILABLE = False
    print("[主窗口] SystemTrayIcon 不可用，将禁用系统托盘功能")
from typing import Optional, Dict, Any

from ..common.config_loader import study_cfg
from ..logging import log_manager
from ..logging.viewer import LogViewer
from ..components.status_bar import StatusBar
from ..components.toolbar import ToolBar


class MainWindow(MSFluentWindow):
    """
    学习工具主窗口类
    
    基于MSFluentWindow实现的主界面，提供完整的用户界面
    """
    
    def __init__(self):
        super().__init__()
        
        # 配置
        self.cfg = study_cfg
        
        # 界面组件
        self.pivot = None
        self.status_bar = None
        self.tool_bar = None
        self.system_tray = None
        
        # 页面实例
        self.user_interface = None
        self.automation_interface = None
        self.course_interface = None
        self.log_viewer = None
        
        # 状态更新定时器
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self._update_status)
        self.status_timer.start(5000)  # 5秒更新一次
        
        self._init_window()
        self._init_navigation()
        self._init_interfaces()
        self._init_system_tray()
        self._connect_signals()
        
        # 记录启动日志
        log_manager.info("学习工具主窗口已启动", module="MainWindow")
    
    def _init_window(self):
        """初始化窗口"""
        # 设置窗口属性
        self.setWindowTitle("学习工具 - 自动化学习辅助系统")
        self.setWindowIcon(QIcon("app/resources/icons/app.ico"))
        
        # 设置窗口大小和位置
        self.resize(1200, 800)
        self.setMinimumSize(1000, 600)
        
        # 居中显示
        screen = QApplication.primaryScreen().geometry()
        size = self.geometry()
        self.move(
            (screen.width() - size.width()) // 2,
            (screen.height() - size.height()) // 2
        )
        
        # 设置主题
        if isDarkTheme():
            setTheme(Theme.DARK)
        else:
            setTheme(Theme.LIGHT)
    
    def _init_navigation(self):
        """初始化导航"""
        # 用户管理
        self.addSubInterface(
            self.get_user_interface(),
            FIF.PEOPLE,
            "用户管理",
            NavigationItemPosition.TOP
        )
        
        # 自动化学习
        self.addSubInterface(
            self.get_automation_interface(),
            FIF.PLAY,
            "自动化学习",
            NavigationItemPosition.TOP
        )
        
        # 课程管理
        self.addSubInterface(
            self.get_course_interface(),
            FIF.BOOK_SHELF,
            "课程管理",
            NavigationItemPosition.TOP
        )
        
        # 日志查看
        self.addSubInterface(
            self.get_log_viewer(),
            FIF.HISTORY,
            "日志查看",
            NavigationItemPosition.TOP
        )
        
        # 设置页面
        self.addSubInterface(
            self._create_settings_interface(),
            FIF.SETTING,
            "设置",
            NavigationItemPosition.BOTTOM
        )
        
        # 关于页面
        self.addSubInterface(
            self._create_about_interface(),
            FIF.INFO,
            "关于",
            NavigationItemPosition.BOTTOM
        )
    
    def _init_interfaces(self):
        """初始化界面组件"""
        # 状态栏
        self.status_bar = StatusBar(self)
        self.setStatusBar(self.status_bar)
        
        # 工具栏
        self.tool_bar = ToolBar(self)
        self.addToolBar(self.tool_bar)
    
    def _init_system_tray(self):
        """初始化系统托盘"""
        if not SYSTEM_TRAY_AVAILABLE:
            log_manager.warning("SystemTrayIcon 不可用，跳过系统托盘初始化", module="MainWindow")
            return

        try:
            # 创建系统托盘图标
            self.system_tray = SystemTrayIcon(QIcon("app/resources/icons/app.ico"), self)
            
            # 创建托盘菜单
            tray_menu = SystemTrayMenu(parent=self)
            
            # 添加菜单项
            show_action = Action(FIF.HOME, "显示主窗口")
            show_action.triggered.connect(self.show_window)
            tray_menu.addAction(show_action)
            
            hide_action = Action(FIF.MINIMIZE, "隐藏到托盘")
            hide_action.triggered.connect(self.hide)
            tray_menu.addAction(hide_action)
            
            tray_menu.addSeparator()
            
            exit_action = Action(FIF.CLOSE, "退出程序")
            exit_action.triggered.connect(self.close_application)
            tray_menu.addAction(exit_action)
            
            # 设置托盘菜单
            self.system_tray.setContextMenu(tray_menu)
            
            # 连接托盘信号
            self.system_tray.activated.connect(self._on_tray_activated)
            
            # 显示托盘图标
            self.system_tray.show()
            
        except Exception as e:
            log_manager.warning(f"初始化系统托盘失败: {e}", module="MainWindow")
    
    def _connect_signals(self):
        """连接信号"""
        # 主题变化信号
        self.cfg.themeChanged.connect(self._on_theme_changed)
    
    def get_user_interface(self) -> QWidget:
        """获取用户管理界面"""
        if self.user_interface is None:
            # 临时占位符界面
            self.user_interface = QWidget()
            layout = QVBoxLayout(self.user_interface)
            label = SubtitleLabel("用户管理界面")
            label.setAlignment(Qt.AlignCenter)
            layout.addWidget(label)
        return self.user_interface

    def get_automation_interface(self) -> QWidget:
        """获取自动化学习界面"""
        if self.automation_interface is None:
            # 临时占位符界面
            self.automation_interface = QWidget()
            layout = QVBoxLayout(self.automation_interface)
            label = SubtitleLabel("自动化学习界面")
            label.setAlignment(Qt.AlignCenter)
            layout.addWidget(label)
        return self.automation_interface

    def get_course_interface(self) -> QWidget:
        """获取课程管理界面"""
        if self.course_interface is None:
            # 临时占位符界面
            self.course_interface = QWidget()
            layout = QVBoxLayout(self.course_interface)
            label = SubtitleLabel("课程管理界面")
            label.setAlignment(Qt.AlignCenter)
            layout.addWidget(label)
        return self.course_interface
    
    def get_log_viewer(self) -> LogViewer:
        """获取日志查看器"""
        if self.log_viewer is None:
            self.log_viewer = LogViewer(self)
        return self.log_viewer
    
    def _create_settings_interface(self) -> QWidget:
        """创建设置界面"""
        from ..components.settings_interface import SettingsInterface
        return SettingsInterface(self)
    
    def _create_about_interface(self) -> QWidget:
        """创建关于界面"""
        from ..components.about_interface import AboutInterface
        return AboutInterface(self)
    
    def _update_status(self):
        """更新状态信息"""
        try:
            if self.status_bar:
                self.status_bar.update_status()
        except Exception as e:
            log_manager.error(f"更新状态失败: {e}", module="MainWindow")
    
    def _on_theme_changed(self, theme):
        """主题变化处理"""
        setTheme(theme)
        log_manager.info(f"主题已切换为: {theme.value}", module="MainWindow")
    
    def _on_tray_activated(self, reason):
        """托盘图标激活处理"""
        if SYSTEM_TRAY_AVAILABLE and reason == SystemTrayIcon.DoubleClick:
            self.show_window()
    
    def show_window(self):
        """显示窗口"""
        self.show()
        self.raise_()
        self.activateWindow()
    
    def close_application(self):
        """关闭应用程序"""
        # 记录关闭日志
        log_manager.info("学习工具正在关闭", module="MainWindow")
        
        # 停止定时器
        if self.status_timer.isActive():
            self.status_timer.stop()
        
        # 隐藏托盘图标
        if self.system_tray:
            self.system_tray.hide()
        
        # 关闭应用程序
        QApplication.quit()
    
    def closeEvent(self, event):
        """关闭事件处理"""
        # 最小化到托盘而不是直接关闭
        if self.system_tray and self.system_tray.isVisible():
            self.hide()
            self.system_tray.showMessage(
                "学习工具",
                "程序已最小化到系统托盘",
                QIcon("app/resources/icons/app.ico"),
                2000
            )
            event.ignore()
        else:
            self.close_application()
            event.accept()
    
    def show_info_bar(self, title: str, content: str, 
                     duration: int = 2000, position: InfoBarPosition = InfoBarPosition.TOP):
        """显示信息栏"""
        InfoBar.success(
            title=title,
            content=content,
            orient=Qt.Horizontal,
            isClosable=True,
            position=position,
            duration=duration,
            parent=self
        )
    
    def show_error_bar(self, title: str, content: str,
                      duration: int = 3000, position: InfoBarPosition = InfoBarPosition.TOP):
        """显示错误栏"""
        InfoBar.error(
            title=title,
            content=content,
            orient=Qt.Horizontal,
            isClosable=True,
            position=position,
            duration=duration,
            parent=self
        )
    
    def show_warning_bar(self, title: str, content: str,
                        duration: int = 2500, position: InfoBarPosition = InfoBarPosition.TOP):
        """显示警告栏"""
        InfoBar.warning(
            title=title,
            content=content,
            orient=Qt.Horizontal,
            isClosable=True,
            position=position,
            duration=duration,
            parent=self
        )
    
    def get_current_interface(self) -> Optional[QWidget]:
        """获取当前显示的界面"""
        return self.stackedWidget.currentWidget()
    
    def switch_to_interface(self, interface_name: str):
        """切换到指定界面"""
        interface_map = {
            'user': self.get_user_interface(),
            'automation': self.get_automation_interface(),
            'course': self.get_course_interface(),
            'log': self.get_log_viewer()
        }
        
        if interface_name in interface_map:
            interface = interface_map[interface_name]
            self.stackedWidget.setCurrentWidget(interface)
            log_manager.info(f"切换到界面: {interface_name}", module="MainWindow")
    
    def refresh_current_interface(self):
        """刷新当前界面"""
        current_widget = self.get_current_interface()
        if hasattr(current_widget, 'refresh'):
            current_widget.refresh()
            log_manager.info("当前界面已刷新", module="MainWindow")
    
    def get_window_state(self) -> Dict[str, Any]:
        """获取窗口状态"""
        return {
            'geometry': {
                'x': self.x(),
                'y': self.y(),
                'width': self.width(),
                'height': self.height()
            },
            'is_maximized': self.isMaximized(),
            'is_visible': self.isVisible(),
            'current_interface': self.stackedWidget.currentIndex()
        }
    
    def restore_window_state(self, state: Dict[str, Any]):
        """恢复窗口状态"""
        try:
            geometry = state.get('geometry', {})
            if geometry:
                self.setGeometry(
                    geometry.get('x', 100),
                    geometry.get('y', 100),
                    geometry.get('width', 1200),
                    geometry.get('height', 800)
                )
            
            if state.get('is_maximized', False):
                self.showMaximized()
            
            current_index = state.get('current_interface', 0)
            if 0 <= current_index < self.stackedWidget.count():
                self.stackedWidget.setCurrentIndex(current_index)
            
            log_manager.info("窗口状态已恢复", module="MainWindow")
            
        except Exception as e:
            log_manager.error(f"恢复窗口状态失败: {e}", module="MainWindow")
