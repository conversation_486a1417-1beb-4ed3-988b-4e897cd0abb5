# coding:utf-8
"""
学习处理器

该模块提供自动学习功能，包括课程浏览、视频播放、
进度跟踪等。

主要功能：
- 课程列表获取
- 自动播放视频
- 学习进度跟踪
- 学习状态监控

类说明：
- StudyHandler: 学习处理器类
"""

import asyncio
import time
from typing import Optional, Dict, Any, List
from playwright.async_api import Page

from ..common.config_loader import get_study_config
from ..database.dao import DAOFactory


class StudyHandler:
    """
    学习处理器类
    
    提供自动学习功能，支持课程浏览和视频播放
    """
    
    def __init__(self, page: Optional[Page] = None):
        """
        初始化学习处理器
        
        Args:
            page: Playwright页面对象
        """
        self.page = page
        self.is_studying = False
        self.current_course = {}
        self.study_progress = {}
        
        # 配置
        cfg = get_study_config()
        self.base_url = cfg.get(cfg.baseUrl)
        self.study_interval = cfg.get(cfg.studyInterval)
        self.auto_next = cfg.get(cfg.autoNext)
        
        # 数据库DAO
        self.course_dao = DAOFactory.get_course_dao()
        self.log_dao = DAOFactory.get_log_dao()
        
        # 学习页面选择器
        self.selectors = {
            'course_list': '.course-item',
            'course_title': '.course-title',
            'course_progress': '.progress-bar',
            'video_player': 'video',
            'play_button': '.play-btn',
            'next_button': '.next-btn',
            'complete_button': '.complete-btn'
        }
        
        print("[学习] 学习处理器初始化完成")
    
    def set_page(self, page: Page):
        """
        设置Playwright页面对象
        
        Args:
            page: Playwright页面对象
        """
        self.page = page
        print("[学习] 页面对象已设置")
    
    async def start_study(self, user_phone: str) -> bool:
        """
        开始自动学习
        
        Args:
            user_phone: 用户手机号
            
        Returns:
            bool: 启动是否成功
        """
        if not self.page:
            print("[学习] 页面对象未设置")
            return False
        
        if self.is_studying:
            print("[学习] 学习已在进行中")
            return True
        
        try:
            print(f"[学习] 开始自动学习 - 用户: {user_phone}")
            
            # 导航到学习页面
            if not await self._navigate_to_study_page():
                return False
            
            # 获取课程列表
            courses = await self._get_course_list()
            if not courses:
                print("[学习] 未找到可学习的课程")
                return False
            
            self.is_studying = True
            
            # 开始学习课程
            for course in courses:
                if not self.is_studying:
                    break
                
                await self._study_course(course, user_phone)
                
                if self.auto_next:
                    await asyncio.sleep(self.study_interval)
                else:
                    break
            
            print("[学习] 自动学习完成")
            return True
            
        except Exception as e:
            print(f"[学习] 自动学习失败: {e}")
            return False
        finally:
            self.is_studying = False
    
    async def stop_study(self):
        """停止自动学习"""
        if not self.is_studying:
            print("[学习] 学习未在进行中")
            return
        
        self.is_studying = False
        print("[学习] 自动学习已停止")
    
    async def _navigate_to_study_page(self) -> bool:
        """
        导航到学习页面
        
        Returns:
            bool: 导航是否成功
        """
        try:
            study_url = f"{self.base_url}/study/data"
            print(f"[学习] 导航到学习页面: {study_url}")
            
            await self.page.goto(study_url)
            await self.page.wait_for_load_state("networkidle")
            
            # 检查是否成功到达学习页面
            current_url = self.page.url
            if "/study/data" in current_url:
                print("[学习] 学习页面加载完成")
                return True
            else:
                print(f"[学习] 未能到达学习页面，当前URL: {current_url}")
                return False
                
        except Exception as e:
            print(f"[学习] 导航到学习页面失败: {e}")
            return False
    
    async def _get_course_list(self) -> List[Dict[str, Any]]:
        """
        获取课程列表
        
        Returns:
            List[Dict[str, Any]]: 课程列表
        """
        try:
            print("[学习] 获取课程列表")
            
            # 等待课程列表加载
            await self.page.wait_for_selector(self.selectors['course_list'], timeout=10000)
            
            # 获取所有课程元素
            course_elements = await self.page.query_selector_all(self.selectors['course_list'])
            
            courses = []
            for i, element in enumerate(course_elements):
                try:
                    # 获取课程标题
                    title_element = await element.query_selector(self.selectors['course_title'])
                    title = await title_element.text_content() if title_element else f"课程{i+1}"
                    
                    # 获取课程进度
                    progress_element = await element.query_selector(self.selectors['course_progress'])
                    progress = 0
                    if progress_element:
                        progress_text = await progress_element.get_attribute('data-progress')
                        if progress_text:
                            progress = float(progress_text)
                    
                    course_info = {
                        'index': i,
                        'title': title.strip(),
                        'progress': progress,
                        'element': element,
                        'completed': progress >= 100
                    }
                    
                    courses.append(course_info)
                    print(f"[学习] 发现课程: {title} (进度: {progress}%)")
                    
                except Exception as e:
                    print(f"[学习] 解析课程信息失败: {e}")
                    continue
            
            print(f"[学习] 共找到 {len(courses)} 门课程")
            return courses
            
        except Exception as e:
            print(f"[学习] 获取课程列表失败: {e}")
            return []
    
    async def _study_course(self, course: Dict[str, Any], user_phone: str):
        """
        学习指定课程
        
        Args:
            course: 课程信息
            user_phone: 用户手机号
        """
        try:
            course_title = course['title']
            course_progress = course['progress']
            
            print(f"[学习] 开始学习课程: {course_title}")
            
            # 如果课程已完成，跳过
            if course['completed']:
                print(f"[学习] 课程已完成，跳过: {course_title}")
                return
            
            # 点击课程进入学习
            await course['element'].click()
            await self.page.wait_for_load_state("networkidle")
            
            # 更新当前课程信息
            self.current_course = course
            
            # 记录学习开始
            await self._log_study_action(user_phone, f"开始学习课程: {course_title}")
            
            # 处理视频播放
            await self._handle_video_playback()
            
            # 等待学习完成
            await self._wait_for_completion()
            
            # 记录学习完成
            await self._log_study_action(user_phone, f"完成学习课程: {course_title}")
            
            print(f"[学习] 课程学习完成: {course_title}")
            
        except Exception as e:
            print(f"[学习] 学习课程失败: {course['title']} - {e}")
    
    async def _handle_video_playback(self):
        """处理视频播放"""
        try:
            print("[学习] 处理视频播放")
            
            # 查找视频播放器
            video_element = await self.page.query_selector(self.selectors['video_player'])
            if not video_element:
                print("[学习] 未找到视频播放器")
                return
            
            # 检查视频是否已在播放
            is_paused = await video_element.evaluate("video => video.paused")
            if is_paused:
                print("[学习] 视频已暂停，尝试播放")
                
                # 尝试点击播放按钮
                play_button = await self.page.query_selector(self.selectors['play_button'])
                if play_button:
                    await play_button.click()
                else:
                    # 直接点击视频元素
                    await video_element.click()
                
                await asyncio.sleep(2)
            
            # 获取视频时长和当前时间
            duration = await video_element.evaluate("video => video.duration")
            current_time = await video_element.evaluate("video => video.currentTime")
            
            print(f"[学习] 视频信息 - 时长: {duration:.1f}s, 当前: {current_time:.1f}s")
            
            # 监控视频播放进度
            await self._monitor_video_progress(video_element)
            
        except Exception as e:
            print(f"[学习] 处理视频播放失败: {e}")
    
    async def _monitor_video_progress(self, video_element):
        """
        监控视频播放进度
        
        Args:
            video_element: 视频元素
        """
        try:
            print("[学习] 开始监控视频播放进度")
            
            last_time = 0
            stuck_count = 0
            
            while self.is_studying:
                # 获取当前播放时间
                current_time = await video_element.evaluate("video => video.currentTime")
                duration = await video_element.evaluate("video => video.duration")
                is_ended = await video_element.evaluate("video => video.ended")
                
                # 计算播放进度
                progress = (current_time / duration * 100) if duration > 0 else 0
                
                print(f"[学习] 视频播放进度: {progress:.1f}% ({current_time:.1f}s/{duration:.1f}s)")
                
                # 检查视频是否播放完成
                if is_ended or progress >= 99:
                    print("[学习] 视频播放完成")
                    break
                
                # 检查视频是否卡住
                if abs(current_time - last_time) < 0.1:
                    stuck_count += 1
                    if stuck_count > 5:
                        print("[学习] 视频播放可能卡住，尝试重新播放")
                        await video_element.click()
                        stuck_count = 0
                else:
                    stuck_count = 0
                
                last_time = current_time
                await asyncio.sleep(5)  # 每5秒检查一次
            
        except Exception as e:
            print(f"[学习] 监控视频播放进度失败: {e}")
    
    async def _wait_for_completion(self):
        """等待学习完成"""
        try:
            print("[学习] 等待学习完成")
            
            # 等待完成按钮出现或自动跳转
            timeout = 300  # 5分钟超时
            start_time = time.time()
            
            while time.time() - start_time < timeout and self.is_studying:
                # 检查是否有完成按钮
                complete_button = await self.page.query_selector(self.selectors['complete_button'])
                if complete_button:
                    print("[学习] 发现完成按钮，点击完成")
                    await complete_button.click()
                    break
                
                # 检查是否有下一步按钮
                next_button = await self.page.query_selector(self.selectors['next_button'])
                if next_button:
                    print("[学习] 发现下一步按钮，点击继续")
                    await next_button.click()
                
                await asyncio.sleep(2)
            
            print("[学习] 学习完成等待结束")
            
        except Exception as e:
            print(f"[学习] 等待学习完成失败: {e}")
    
    async def _log_study_action(self, user_phone: str, message: str):
        """
        记录学习操作日志
        
        Args:
            user_phone: 用户手机号
            message: 日志消息
        """
        try:
            log_data = {
                'user_phone': user_phone,
                'level': 'INFO',
                'message': f"[自动学习] {message}",
                'module': 'StudyHandler',
                'timestamp': time.time()
            }
            
            self.log_dao.create(log_data)
            
        except Exception as e:
            print(f"[学习] 记录学习日志失败: {e}")
    
    def get_current_course(self) -> Dict[str, Any]:
        """
        获取当前学习课程
        
        Returns:
            Dict[str, Any]: 当前课程信息
        """
        return self.current_course.copy()
    
    def get_study_progress(self) -> Dict[str, Any]:
        """
        获取学习进度
        
        Returns:
            Dict[str, Any]: 学习进度信息
        """
        return self.study_progress.copy()
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取学习状态
        
        Returns:
            Dict[str, Any]: 状态信息
        """
        return {
            "is_studying": self.is_studying,
            "current_course": self.current_course,
            "study_progress": self.study_progress,
            "auto_next": self.auto_next,
            "study_interval": self.study_interval,
            "has_page": self.page is not None
        }
