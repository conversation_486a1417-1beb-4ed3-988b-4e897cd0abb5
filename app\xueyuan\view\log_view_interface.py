# coding:utf-8
"""
日志查看界面

该模块定义了日志查看界面类 LogViewInterface，
提供系统日志的查看、筛选和管理功能。

主要功能：
- 显示系统日志
- 日志级别筛选
- 日志搜索和清理

类说明：
- LogViewInterface: 日志查看界面类
"""

from PySide6.QtCore import Qt, QTimer
from PySide6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout
from qfluentwidgets import (CardWidget, HeaderCardWidget, BodyLabel, LineEdit,
                            PushButton, TableWidget, ComboBox, FluentIcon as FIF,
                            InfoBar, MessageBox)

from ..database.dao import DAOFactory
from ..common.constants import LogLevel


class LogViewInterface(QWidget):
    """日志查看界面类"""
    
    def __init__(self, parent=None):
        """初始化日志查看界面"""
        super().__init__(parent)
        self.setObjectName("LogViewInterface")
        
        # 初始化界面
        self.initWidget()
        self.initLayout()
        self.connectSignalToSlot()
        
        # 启动定时器更新日志
        self.updateTimer = QTimer()
        self.updateTimer.timeout.connect(self.refreshLogs)
        self.updateTimer.start(5000)  # 每5秒更新一次
        
        # 初始加载日志
        self.refreshLogs()

    def initWidget(self):
        """初始化界面组件"""
        # 创建主布局
        self.vBoxLayout = QVBoxLayout(self)
        
        # 创建操作面板卡片
        self.operationCard = HeaderCardWidget(self)
        self.operationCard.setTitle("日志操作")
        
        # 操作面板内容
        self.operationWidget = QWidget()
        self.operationLayout = QHBoxLayout(self.operationWidget)
        
        # 刷新按钮
        self.refreshBtn = PushButton("刷新", self)
        self.refreshBtn.setIcon(FIF.SYNC)
        self.refreshBtn.setFixedSize(80, 35)
        
        # 清理日志按钮
        self.cleanBtn = PushButton("清理日志", self)
        self.cleanBtn.setIcon(FIF.DELETE)
        self.cleanBtn.setFixedSize(100, 35)
        
        # 导出日志按钮
        self.exportBtn = PushButton("导出日志", self)
        self.exportBtn.setIcon(FIF.SAVE)
        self.exportBtn.setFixedSize(100, 35)
        
        # 日志级别筛选
        self.levelFilterCombo = ComboBox()
        self.levelFilterCombo.addItems(["全部级别", "DEBUG", "INFO", "WARNING", "ERROR"])
        self.levelFilterCombo.setFixedSize(120, 35)
        
        # 搜索框
        self.searchEdit = LineEdit()
        self.searchEdit.setPlaceholderText("搜索日志内容...")
        self.searchEdit.setFixedSize(200, 35)
        
        # 添加到操作布局
        self.operationLayout.addWidget(self.refreshBtn)
        self.operationLayout.addWidget(self.cleanBtn)
        self.operationLayout.addWidget(self.exportBtn)
        self.operationLayout.addStretch()
        self.operationLayout.addWidget(BodyLabel("级别筛选:"))
        self.operationLayout.addWidget(self.levelFilterCombo)
        self.operationLayout.addWidget(self.searchEdit)
        
        # 设置操作卡片内容
        self.operationCard.setContent(self.operationWidget)
        
        # 创建日志列表卡片
        self.logListCard = HeaderCardWidget(self)
        self.logListCard.setTitle("日志列表")
        
        # 日志表格
        self.logTable = TableWidget()
        self.logTable.setColumnCount(5)
        self.logTable.setHorizontalHeaderLabels([
            "时间", "级别", "模块", "用户", "消息"
        ])
        
        # 设置列宽
        self.logTable.setColumnWidth(0, 150)  # 时间
        self.logTable.setColumnWidth(1, 80)   # 级别
        self.logTable.setColumnWidth(2, 150)  # 模块
        self.logTable.setColumnWidth(3, 120)  # 用户
        self.logTable.setColumnWidth(4, 400)  # 消息
        
        # 设置日志列表卡片内容
        self.logListCard.setContent(self.logTable)

    def initLayout(self):
        """初始化布局"""
        self.vBoxLayout.setContentsMargins(0, 0, 0, 0)
        self.vBoxLayout.setSpacing(20)
        
        self.vBoxLayout.addWidget(self.operationCard)
        self.vBoxLayout.addWidget(self.logListCard)

    def connectSignalToSlot(self):
        """连接信号到槽函数"""
        self.refreshBtn.clicked.connect(self.refreshLogs)
        self.cleanBtn.clicked.connect(self.onCleanLogs)
        self.exportBtn.clicked.connect(self.onExportLogs)
        self.levelFilterCombo.currentTextChanged.connect(self.onFilterChanged)
        self.searchEdit.textChanged.connect(self.onSearchChanged)

    def onCleanLogs(self):
        """清理日志"""
        dialog = MessageBox("确认清理", "确定要清理30天前的日志吗？", self)
        if dialog.exec():
            log_dao = DAOFactory.get_log_dao()
            if log_dao.clean_old_logs(30):
                self.showInfoBar("日志清理成功", "success")
                self.refreshLogs()
            else:
                self.showInfoBar("日志清理失败", "error")

    def onExportLogs(self):
        """导出日志"""
        # 这里应该实现导出逻辑
        self.showInfoBar("导出功能开发中...", "info")

    def onFilterChanged(self):
        """筛选条件改变"""
        self.refreshLogs()

    def onSearchChanged(self):
        """搜索内容改变"""
        self.refreshLogs()

    def refreshLogs(self):
        """刷新日志列表"""
        try:
            log_dao = DAOFactory.get_log_dao()
            
            # 获取筛选条件
            level_filter = self.levelFilterCombo.currentText()
            search_text = self.searchEdit.text().strip()
            
            # 获取日志列表
            if level_filter == "全部级别":
                logs = log_dao.get_all()
            else:
                logs = log_dao.get_by_level(level_filter, 1000)
            
            # 应用搜索筛选
            if search_text:
                logs = [log for log in logs if search_text in log.message]
            
            # 更新表格
            self.updateLogTable(logs)
            
        except Exception as e:
            print(f"[界面] 刷新日志列表失败: {e}")
            self.showInfoBar("刷新日志列表失败", "error")

    def updateLogTable(self, logs):
        """更新日志表格"""
        try:
            self.logTable.setRowCount(len(logs))
            
            for i, log in enumerate(logs):
                self.logTable.setItem(i, 0, log.created_at.strftime("%Y-%m-%d %H:%M:%S") if log.created_at else "")
                self.logTable.setItem(i, 1, log.level)
                self.logTable.setItem(i, 2, log.module or "")
                self.logTable.setItem(i, 3, log.user_phone or "系统")
                self.logTable.setItem(i, 4, log.message)
                
        except Exception as e:
            print(f"[界面] 更新日志表格失败: {e}")

    def showInfoBar(self, message: str, type: str = "info"):
        """显示信息栏"""
        if type == "success":
            InfoBar.success("", message, duration=3000, parent=self)
        elif type == "warning":
            InfoBar.warning("", message, duration=5000, parent=self)
        elif type == "error":
            InfoBar.error("", message, duration=8000, parent=self)
        else:
            InfoBar.info("", message, duration=3000, parent=self)
