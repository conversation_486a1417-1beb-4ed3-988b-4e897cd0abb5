# coding:utf-8
"""
进度跟踪器

该模块提供学习进度跟踪功能，包括进度计算、
里程碑检测、成就统计等。

主要功能：
- 学习进度计算和更新
- 里程碑检测和通知
- 成就统计和分析
- 进度报告生成

类说明：
- ProgressTracker: 进度跟踪器类
"""

from typing import Optional, Dict, Any, List, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass

from ..database.dao import DAOFactory


@dataclass
class Milestone:
    """里程碑数据类"""
    id: str
    name: str
    description: str
    condition: str  # 达成条件
    reward_points: int = 0
    achieved: bool = False
    achieved_at: Optional[datetime] = None


@dataclass
class Achievement:
    """成就数据类"""
    id: str
    name: str
    description: str
    category: str
    points: int
    unlocked: bool = False
    unlocked_at: Optional[datetime] = None


class ProgressTracker:
    """
    进度跟踪器类
    
    提供学习进度跟踪和成就系统功能
    """
    
    def __init__(self):
        """初始化进度跟踪器"""
        # 数据库DAO
        self.progress_dao = DAOFactory.get_progress_dao()
        self.course_dao = DAOFactory.get_course_dao()
        self.log_dao = DAOFactory.get_log_dao()
        
        # 里程碑定义
        self.milestones = self._init_milestones()
        
        # 成就定义
        self.achievements = self._init_achievements()
        
        # 回调函数
        self.callbacks: Dict[str, List[Callable]] = {
            'milestone_achieved': [],
            'achievement_unlocked': [],
            'progress_updated': [],
            'course_completed': []
        }
        
        print("[进度跟踪] 进度跟踪器初始化完成")
    
    def _init_milestones(self) -> List[Milestone]:
        """初始化里程碑"""
        return [
            Milestone(
                id="first_video",
                name="初次观看",
                description="完成第一个视频的观看",
                condition="completed_videos >= 1",
                reward_points=10
            ),
            Milestone(
                id="first_course",
                name="课程完成",
                description="完成第一门课程",
                condition="completed_courses >= 1",
                reward_points=50
            ),
            Milestone(
                id="week_streak",
                name="连续学习",
                description="连续7天学习",
                condition="learning_streak >= 7",
                reward_points=30
            ),
            Milestone(
                id="ten_videos",
                name="视频达人",
                description="累计观看10个视频",
                condition="total_videos_watched >= 10",
                reward_points=25
            ),
            Milestone(
                id="hundred_videos",
                name="视频专家",
                description="累计观看100个视频",
                condition="total_videos_watched >= 100",
                reward_points=100
            ),
            Milestone(
                id="five_courses",
                name="学习能手",
                description="完成5门课程",
                condition="completed_courses >= 5",
                reward_points=150
            ),
            Milestone(
                id="month_active",
                name="月度活跃",
                description="一个月内学习时长超过20小时",
                condition="monthly_learning_hours >= 20",
                reward_points=80
            )
        ]
    
    def _init_achievements(self) -> List[Achievement]:
        """初始化成就"""
        return [
            Achievement(
                id="early_bird",
                name="早起鸟",
                description="在早上6-8点之间学习",
                category="时间管理",
                points=15
            ),
            Achievement(
                id="night_owl",
                name="夜猫子",
                description="在晚上10点后学习",
                category="时间管理",
                points=15
            ),
            Achievement(
                id="speed_learner",
                name="快速学习者",
                description="一天内完成一门课程",
                category="学习效率",
                points=40
            ),
            Achievement(
                id="perfectionist",
                name="完美主义者",
                description="连续完成5门课程且进度100%",
                category="学习质量",
                points=60
            ),
            Achievement(
                id="explorer",
                name="探索者",
                description="学习超过3个不同类别的课程",
                category="学习广度",
                points=35
            ),
            Achievement(
                id="marathon_learner",
                name="马拉松学习者",
                description="单次学习时长超过4小时",
                category="学习耐力",
                points=50
            ),
            Achievement(
                id="consistent_learner",
                name="坚持学习者",
                description="连续30天每天都有学习记录",
                category="学习习惯",
                points=100
            )
        ]
    
    async def update_progress(self, user_phone: str, course_id: str, 
                            video_completed: bool = False, 
                            chapter_completed: bool = False,
                            course_completed: bool = False) -> Dict[str, Any]:
        """
        更新学习进度
        
        Args:
            user_phone: 用户手机号
            course_id: 课程ID
            video_completed: 是否完成视频
            chapter_completed: 是否完成章节
            course_completed: 是否完成课程
            
        Returns:
            Dict[str, Any]: 更新结果
        """
        try:
            print(f"[进度跟踪] 更新学习进度: {user_phone} - {course_id}")
            
            # 获取当前进度
            progress = self.progress_dao.get_by_course(user_phone, course_id)
            if not progress:
                print("[进度跟踪] 进度记录不存在")
                return {'success': False, 'message': '进度记录不存在'}
            
            # 更新进度数据
            update_data = {'updated_at': datetime.now()}
            
            if video_completed:
                update_data['completed_videos'] = progress.get('completed_videos', 0) + 1
                
                # 重新计算进度百分比
                total_videos = progress.get('total_videos', 1)
                completed_videos = update_data['completed_videos']
                progress_percentage = (completed_videos / total_videos) * 100
                update_data['progress_percentage'] = min(progress_percentage, 100.0)
            
            if chapter_completed:
                update_data['completed_chapters'] = progress.get('completed_chapters', 0) + 1
            
            if course_completed:
                update_data['status'] = 'completed'
                update_data['completion_time'] = datetime.now()
                update_data['progress_percentage'] = 100.0
            
            # 更新数据库
            updated_progress = self.progress_dao.update(progress['id'], update_data)
            
            if updated_progress:
                # 检查里程碑和成就
                await self._check_milestones(user_phone)
                await self._check_achievements(user_phone)
                
                # 触发进度更新回调
                await self._trigger_callbacks('progress_updated', {
                    'user_phone': user_phone,
                    'course_id': course_id,
                    'progress': updated_progress,
                    'video_completed': video_completed,
                    'chapter_completed': chapter_completed,
                    'course_completed': course_completed
                })
                
                if course_completed:
                    await self._trigger_callbacks('course_completed', {
                        'user_phone': user_phone,
                        'course_id': course_id,
                        'progress': updated_progress
                    })
                
                # 记录日志
                await self._log_progress_event(user_phone, f"进度更新: {course_id}")
                
                return {
                    'success': True,
                    'progress': updated_progress,
                    'message': '进度更新成功'
                }
            else:
                return {'success': False, 'message': '进度更新失败'}
            
        except Exception as e:
            print(f"[进度跟踪] 更新进度失败: {e}")
            return {'success': False, 'message': f'更新失败: {str(e)}'}
    
    async def _check_milestones(self, user_phone: str):
        """
        检查里程碑达成情况
        
        Args:
            user_phone: 用户手机号
        """
        try:
            # 获取用户统计数据
            stats = await self._get_user_stats(user_phone)
            
            for milestone in self.milestones:
                if milestone.achieved:
                    continue
                
                # 检查里程碑条件
                if self._evaluate_condition(milestone.condition, stats):
                    milestone.achieved = True
                    milestone.achieved_at = datetime.now()
                    
                    # 触发里程碑达成回调
                    await self._trigger_callbacks('milestone_achieved', {
                        'user_phone': user_phone,
                        'milestone': milestone,
                        'stats': stats
                    })
                    
                    # 记录日志
                    await self._log_progress_event(
                        user_phone, 
                        f"里程碑达成: {milestone.name} - {milestone.description}"
                    )
                    
                    print(f"[进度跟踪] 里程碑达成: {milestone.name}")
            
        except Exception as e:
            print(f"[进度跟踪] 检查里程碑失败: {e}")
    
    async def _check_achievements(self, user_phone: str):
        """
        检查成就解锁情况
        
        Args:
            user_phone: 用户手机号
        """
        try:
            # 获取用户统计数据
            stats = await self._get_user_stats(user_phone)
            
            for achievement in self.achievements:
                if achievement.unlocked:
                    continue
                
                # 检查成就条件
                unlocked = False
                
                if achievement.id == "early_bird":
                    unlocked = await self._check_early_bird_achievement(user_phone)
                elif achievement.id == "night_owl":
                    unlocked = await self._check_night_owl_achievement(user_phone)
                elif achievement.id == "speed_learner":
                    unlocked = await self._check_speed_learner_achievement(user_phone)
                elif achievement.id == "perfectionist":
                    unlocked = await self._check_perfectionist_achievement(user_phone)
                elif achievement.id == "explorer":
                    unlocked = await self._check_explorer_achievement(user_phone)
                elif achievement.id == "marathon_learner":
                    unlocked = await self._check_marathon_learner_achievement(user_phone)
                elif achievement.id == "consistent_learner":
                    unlocked = await self._check_consistent_learner_achievement(user_phone)
                
                if unlocked:
                    achievement.unlocked = True
                    achievement.unlocked_at = datetime.now()
                    
                    # 触发成就解锁回调
                    await self._trigger_callbacks('achievement_unlocked', {
                        'user_phone': user_phone,
                        'achievement': achievement,
                        'stats': stats
                    })
                    
                    # 记录日志
                    await self._log_progress_event(
                        user_phone, 
                        f"成就解锁: {achievement.name} - {achievement.description}"
                    )
                    
                    print(f"[进度跟踪] 成就解锁: {achievement.name}")
            
        except Exception as e:
            print(f"[进度跟踪] 检查成就失败: {e}")
    
    async def _get_user_stats(self, user_phone: str) -> Dict[str, Any]:
        """
        获取用户统计数据
        
        Args:
            user_phone: 用户手机号
            
        Returns:
            Dict[str, Any]: 统计数据
        """
        try:
            # 获取所有进度记录
            all_progress = self.progress_dao.get_by_user(user_phone)
            
            stats = {
                'total_courses': len(all_progress),
                'completed_courses': 0,
                'total_videos_watched': 0,
                'learning_streak': 0,
                'monthly_learning_hours': 0,
                'completed_chapters': 0
            }
            
            for progress in all_progress:
                if progress.get('status') == 'completed':
                    stats['completed_courses'] += 1
                
                stats['total_videos_watched'] += progress.get('completed_videos', 0)
                stats['completed_chapters'] += progress.get('completed_chapters', 0)
            
            # 计算学习连续天数（简化实现）
            stats['learning_streak'] = await self._calculate_learning_streak(user_phone)
            
            # 计算月度学习时长（简化实现）
            stats['monthly_learning_hours'] = await self._calculate_monthly_hours(user_phone)
            
            return stats
            
        except Exception as e:
            print(f"[进度跟踪] 获取用户统计失败: {e}")
            return {}
    
    def _evaluate_condition(self, condition: str, stats: Dict[str, Any]) -> bool:
        """
        评估条件表达式
        
        Args:
            condition: 条件表达式
            stats: 统计数据
            
        Returns:
            bool: 条件是否满足
        """
        try:
            # 简单的条件评估（实际应用中可以使用更安全的表达式解析器）
            for key, value in stats.items():
                condition = condition.replace(key, str(value))
            
            # 评估表达式
            return eval(condition)
            
        except Exception as e:
            print(f"[进度跟踪] 评估条件失败: {e}")
            return False
    
    async def _calculate_learning_streak(self, user_phone: str) -> int:
        """
        计算学习连续天数
        
        Args:
            user_phone: 用户手机号
            
        Returns:
            int: 连续天数
        """
        try:
            # 简化实现：从日志中统计连续学习天数
            # 实际应用中应该有专门的学习记录表
            return 0  # 占位符
            
        except Exception as e:
            print(f"[进度跟踪] 计算学习连续天数失败: {e}")
            return 0
    
    async def _calculate_monthly_hours(self, user_phone: str) -> float:
        """
        计算月度学习时长
        
        Args:
            user_phone: 用户手机号
            
        Returns:
            float: 学习时长（小时）
        """
        try:
            # 简化实现：从日志中统计月度学习时长
            # 实际应用中应该有专门的学习时长记录
            return 0.0  # 占位符
            
        except Exception as e:
            print(f"[进度跟踪] 计算月度学习时长失败: {e}")
            return 0.0
    
    async def _check_early_bird_achievement(self, user_phone: str) -> bool:
        """检查早起鸟成就"""
        # 简化实现：检查是否在早上6-8点学习过
        return False
    
    async def _check_night_owl_achievement(self, user_phone: str) -> bool:
        """检查夜猫子成就"""
        # 简化实现：检查是否在晚上10点后学习过
        return False
    
    async def _check_speed_learner_achievement(self, user_phone: str) -> bool:
        """检查快速学习者成就"""
        # 简化实现：检查是否一天内完成过课程
        return False
    
    async def _check_perfectionist_achievement(self, user_phone: str) -> bool:
        """检查完美主义者成就"""
        # 简化实现：检查是否连续完成5门课程且进度100%
        return False
    
    async def _check_explorer_achievement(self, user_phone: str) -> bool:
        """检查探索者成就"""
        # 简化实现：检查是否学习过3个不同类别的课程
        return False
    
    async def _check_marathon_learner_achievement(self, user_phone: str) -> bool:
        """检查马拉松学习者成就"""
        # 简化实现：检查是否单次学习超过4小时
        return False
    
    async def _check_consistent_learner_achievement(self, user_phone: str) -> bool:
        """检查坚持学习者成就"""
        # 简化实现：检查是否连续30天学习
        return False
    
    async def _trigger_callbacks(self, event_type: str, data: Dict[str, Any]):
        """
        触发回调函数
        
        Args:
            event_type: 事件类型
            data: 事件数据
        """
        if event_type in self.callbacks:
            for callback in self.callbacks[event_type]:
                try:
                    if hasattr(callback, '__call__'):
                        if hasattr(callback, '__code__') and callback.__code__.co_flags & 0x80:
                            await callback(data)
                        else:
                            callback(data)
                except Exception as e:
                    print(f"[进度跟踪] 回调函数执行失败: {e}")
    
    async def _log_progress_event(self, user_phone: str, message: str, level: str = "INFO"):
        """
        记录进度事件日志
        
        Args:
            user_phone: 用户手机号
            message: 日志消息
            level: 日志级别
        """
        try:
            log_data = {
                'user_phone': user_phone,
                'level': level,
                'message': f"[进度跟踪] {message}",
                'module': 'ProgressTracker',
                'timestamp': datetime.now()
            }
            
            self.log_dao.create(log_data)
            
        except Exception as e:
            print(f"[进度跟踪] 记录进度日志失败: {e}")
    
    def add_callback(self, event_type: str, callback: Callable):
        """
        添加事件回调函数
        
        Args:
            event_type: 事件类型
            callback: 回调函数
        """
        if event_type in self.callbacks:
            self.callbacks[event_type].append(callback)
            print(f"[进度跟踪] 添加事件回调: {event_type}")
        else:
            print(f"[进度跟踪] 未知事件类型: {event_type}")
    
    def get_user_milestones(self, user_phone: str) -> List[Dict[str, Any]]:
        """
        获取用户里程碑
        
        Args:
            user_phone: 用户手机号
            
        Returns:
            List[Dict[str, Any]]: 里程碑列表
        """
        return [
            {
                'id': milestone.id,
                'name': milestone.name,
                'description': milestone.description,
                'reward_points': milestone.reward_points,
                'achieved': milestone.achieved,
                'achieved_at': milestone.achieved_at
            }
            for milestone in self.milestones
        ]
    
    def get_user_achievements(self, user_phone: str) -> List[Dict[str, Any]]:
        """
        获取用户成就
        
        Args:
            user_phone: 用户手机号
            
        Returns:
            List[Dict[str, Any]]: 成就列表
        """
        return [
            {
                'id': achievement.id,
                'name': achievement.name,
                'description': achievement.description,
                'category': achievement.category,
                'points': achievement.points,
                'unlocked': achievement.unlocked,
                'unlocked_at': achievement.unlocked_at
            }
            for achievement in self.achievements
        ]
    
    def get_progress_summary(self, user_phone: str) -> Dict[str, Any]:
        """
        获取进度摘要
        
        Args:
            user_phone: 用户手机号
            
        Returns:
            Dict[str, Any]: 进度摘要
        """
        try:
            # 获取统计数据
            stats = self.progress_dao.get_by_user(user_phone)
            
            # 计算摘要
            summary = {
                'total_courses': len(stats),
                'completed_courses': len([s for s in stats if s.get('status') == 'completed']),
                'total_progress': sum(s.get('progress_percentage', 0) for s in stats) / max(len(stats), 1),
                'achieved_milestones': len([m for m in self.milestones if m.achieved]),
                'unlocked_achievements': len([a for a in self.achievements if a.unlocked]),
                'total_points': sum(m.reward_points for m in self.milestones if m.achieved) + 
                               sum(a.points for a in self.achievements if a.unlocked)
            }
            
            return summary
            
        except Exception as e:
            print(f"[进度跟踪] 获取进度摘要失败: {e}")
            return {}
