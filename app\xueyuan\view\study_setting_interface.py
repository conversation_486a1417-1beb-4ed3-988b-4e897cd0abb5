# coding:utf-8
"""
学习设置界面

该模块定义了学习设置界面类 StudySettingInterface，
提供学习工具的各项配置设置功能。

主要功能：
- 系统设置
- 浏览器设置
- OCR设置
- API设置
- 网站设置

类说明：
- StudySettingInterface: 学习设置界面类
"""

from PySide6.QtCore import Qt
from PySide6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QFormLayout
from qfluentwidgets import (CardWidget, HeaderCardWidget, BodyLabel, LineEdit,
                            SpinBox, ComboBox, SwitchButton, PrimaryPushButton,
                            FluentIcon as FIF, InfoBar, ScrollArea)

from ..common.config_loader import get_study_config, save_study_config
from ..common.constants import BrowserType, OCREngine


class StudySettingInterface(ScrollArea):
    """学习设置界面类"""
    
    def __init__(self, parent=None):
        """初始化学习设置界面"""
        super().__init__(parent)
        self.setObjectName("StudySettingInterface")
        
        # 创建主容器
        self.view = QWidget()
        self.vBoxLayout = QVBoxLayout(self.view)
        
        # 初始化界面
        self.initWidget()
        self.initLayout()
        self.connectSignalToSlot()
        
        # 加载配置
        self.loadSettings()

    def initWidget(self):
        """初始化界面组件"""
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.setWidget(self.view)
        self.setWidgetResizable(True)
        
        # 创建系统设置卡片
        self.systemCard = HeaderCardWidget(self)
        self.systemCard.setTitle("系统设置")
        
        self.systemWidget = QWidget()
        self.systemLayout = QFormLayout(self.systemWidget)
        
        # 系统设置项
        self.maxConcurrentSpinBox = SpinBox()
        self.maxConcurrentSpinBox.setRange(1, 20)
        self.maxConcurrentSpinBox.setValue(5)
        
        self.taskTimeoutSpinBox = SpinBox()
        self.taskTimeoutSpinBox.setRange(60, 3600)
        self.taskTimeoutSpinBox.setValue(300)
        
        self.retryCountSpinBox = SpinBox()
        self.retryCountSpinBox.setRange(1, 10)
        self.retryCountSpinBox.setValue(3)
        
        self.systemLayout.addRow("最大并发数:", self.maxConcurrentSpinBox)
        self.systemLayout.addRow("任务超时(秒):", self.taskTimeoutSpinBox)
        self.systemLayout.addRow("重试次数:", self.retryCountSpinBox)
        
        self.systemCard.setContent(self.systemWidget)
        
        # 创建浏览器设置卡片
        self.browserCard = HeaderCardWidget(self)
        self.browserCard.setTitle("浏览器设置")
        
        self.browserWidget = QWidget()
        self.browserLayout = QFormLayout(self.browserWidget)
        
        # 浏览器设置项
        self.browserTypeCombo = ComboBox()
        self.browserTypeCombo.addItems(["chromium", "firefox", "webkit"])
        
        self.headlessSwitch = SwitchButton()
        self.headlessSwitch.setChecked(True)
        
        self.pageTimeoutSpinBox = SpinBox()
        self.pageTimeoutSpinBox.setRange(10, 120)
        self.pageTimeoutSpinBox.setValue(30)
        
        self.browserLayout.addRow("浏览器类型:", self.browserTypeCombo)
        self.browserLayout.addRow("无头模式:", self.headlessSwitch)
        self.browserLayout.addRow("页面超时(秒):", self.pageTimeoutSpinBox)
        
        self.browserCard.setContent(self.browserWidget)
        
        # 创建OCR设置卡片
        self.ocrCard = HeaderCardWidget(self)
        self.ocrCard.setTitle("OCR设置")
        
        self.ocrWidget = QWidget()
        self.ocrLayout = QFormLayout(self.ocrWidget)
        
        # OCR设置项
        self.primaryEngineCombo = ComboBox()
        self.primaryEngineCombo.addItems(["ddddocr", "baidu"])
        
        self.fallbackEngineCombo = ComboBox()
        self.fallbackEngineCombo.addItems(["baidu", "ddddocr"])
        
        self.baiduAppIdEdit = LineEdit()
        self.baiduAppIdEdit.setPlaceholderText("百度OCR App ID")
        
        self.baiduApiKeyEdit = LineEdit()
        self.baiduApiKeyEdit.setPlaceholderText("百度OCR API Key")
        
        self.baiduSecretKeyEdit = LineEdit()
        self.baiduSecretKeyEdit.setPlaceholderText("百度OCR Secret Key")
        
        self.ocrLayout.addRow("主要引擎:", self.primaryEngineCombo)
        self.ocrLayout.addRow("备用引擎:", self.fallbackEngineCombo)
        self.ocrLayout.addRow("百度App ID:", self.baiduAppIdEdit)
        self.ocrLayout.addRow("百度API Key:", self.baiduApiKeyEdit)
        self.ocrLayout.addRow("百度Secret Key:", self.baiduSecretKeyEdit)
        
        self.ocrCard.setContent(self.ocrWidget)
        
        # 创建API设置卡片
        self.apiCard = HeaderCardWidget(self)
        self.apiCard.setTitle("API设置")
        
        self.apiWidget = QWidget()
        self.apiLayout = QFormLayout(self.apiWidget)
        
        # API设置项
        self.captureEnabledSwitch = SwitchButton()
        self.captureEnabledSwitch.setChecked(True)
        
        self.requestTimeoutSpinBox = SpinBox()
        self.requestTimeoutSpinBox.setRange(5, 60)
        self.requestTimeoutSpinBox.setValue(10)
        
        self.apiLayout.addRow("启用API捕获:", self.captureEnabledSwitch)
        self.apiLayout.addRow("请求超时(秒):", self.requestTimeoutSpinBox)
        
        self.apiCard.setContent(self.apiWidget)
        
        # 创建网站设置卡片
        self.websiteCard = HeaderCardWidget(self)
        self.websiteCard.setTitle("网站设置")
        
        self.websiteWidget = QWidget()
        self.websiteLayout = QFormLayout(self.websiteWidget)
        
        # 网站设置项
        self.baseUrlEdit = LineEdit()
        self.baseUrlEdit.setPlaceholderText("学习网站基础URL")
        
        self.loginUrlEdit = LineEdit()
        self.loginUrlEdit.setPlaceholderText("登录页面URL")
        
        self.studyUrlEdit = LineEdit()
        self.studyUrlEdit.setPlaceholderText("学习页面URL")
        
        self.websiteLayout.addRow("基础URL:", self.baseUrlEdit)
        self.websiteLayout.addRow("登录URL:", self.loginUrlEdit)
        self.websiteLayout.addRow("学习URL:", self.studyUrlEdit)
        
        self.websiteCard.setContent(self.websiteWidget)
        
        # 创建操作按钮
        self.buttonWidget = QWidget()
        self.buttonLayout = QHBoxLayout(self.buttonWidget)
        
        self.saveBtn = PrimaryPushButton("保存设置", self)
        self.saveBtn.setIcon(FIF.SAVE)
        self.saveBtn.setFixedSize(120, 40)
        
        self.resetBtn = PrimaryPushButton("重置设置", self)
        self.resetBtn.setIcon(FIF.SYNC)
        self.resetBtn.setFixedSize(120, 40)
        
        self.buttonLayout.addStretch()
        self.buttonLayout.addWidget(self.saveBtn)
        self.buttonLayout.addWidget(self.resetBtn)

    def initLayout(self):
        """初始化布局"""
        self.vBoxLayout.setContentsMargins(0, 0, 0, 0)
        self.vBoxLayout.setSpacing(20)
        
        self.vBoxLayout.addWidget(self.systemCard)
        self.vBoxLayout.addWidget(self.browserCard)
        self.vBoxLayout.addWidget(self.ocrCard)
        self.vBoxLayout.addWidget(self.apiCard)
        self.vBoxLayout.addWidget(self.websiteCard)
        self.vBoxLayout.addWidget(self.buttonWidget)

    def connectSignalToSlot(self):
        """连接信号到槽函数"""
        self.saveBtn.clicked.connect(self.onSaveSettings)
        self.resetBtn.clicked.connect(self.onResetSettings)

    def loadSettings(self):
        """加载设置"""
        try:
            cfg = get_study_config()
            
            # 系统设置
            self.maxConcurrentSpinBox.setValue(cfg.get(cfg.maxConcurrent))
            self.taskTimeoutSpinBox.setValue(cfg.get(cfg.taskTimeout))
            self.retryCountSpinBox.setValue(cfg.get(cfg.retryCount))
            
            # 浏览器设置
            self.browserTypeCombo.setCurrentText(cfg.get(cfg.browserType))
            self.headlessSwitch.setChecked(cfg.get(cfg.headless))
            self.pageTimeoutSpinBox.setValue(cfg.get(cfg.pageTimeout))
            
            # OCR设置
            self.primaryEngineCombo.setCurrentText(cfg.get(cfg.primaryOCREngine))
            self.fallbackEngineCombo.setCurrentText(cfg.get(cfg.fallbackOCREngine))
            self.baiduAppIdEdit.setText(cfg.get(cfg.baiduAppId))
            self.baiduApiKeyEdit.setText(cfg.get(cfg.baiduApiKey))
            self.baiduSecretKeyEdit.setText(cfg.get(cfg.baiduSecretKey))
            
            # API设置
            self.captureEnabledSwitch.setChecked(cfg.get(cfg.apiCaptureEnabled))
            self.requestTimeoutSpinBox.setValue(cfg.get(cfg.requestTimeout))
            
            # 网站设置
            self.baseUrlEdit.setText(cfg.get(cfg.baseUrl))
            self.loginUrlEdit.setText(cfg.get(cfg.loginUrl))
            self.studyUrlEdit.setText(cfg.get(cfg.studyUrl))
            
        except Exception as e:
            print(f"[设置] 加载设置失败: {e}")
            self.showInfoBar("加载设置失败", "error")

    def onSaveSettings(self):
        """保存设置"""
        try:
            cfg = get_study_config()
            
            # 系统设置
            cfg.set(cfg.maxConcurrent, self.maxConcurrentSpinBox.value())
            cfg.set(cfg.taskTimeout, self.taskTimeoutSpinBox.value())
            cfg.set(cfg.retryCount, self.retryCountSpinBox.value())
            
            # 浏览器设置
            cfg.set(cfg.browserType, self.browserTypeCombo.currentText())
            cfg.set(cfg.headless, self.headlessSwitch.isChecked())
            cfg.set(cfg.pageTimeout, self.pageTimeoutSpinBox.value())
            
            # OCR设置
            cfg.set(cfg.primaryOCREngine, self.primaryEngineCombo.currentText())
            cfg.set(cfg.fallbackOCREngine, self.fallbackEngineCombo.currentText())
            cfg.set(cfg.baiduAppId, self.baiduAppIdEdit.text())
            cfg.set(cfg.baiduApiKey, self.baiduApiKeyEdit.text())
            cfg.set(cfg.baiduSecretKey, self.baiduSecretKeyEdit.text())
            
            # API设置
            cfg.set(cfg.apiCaptureEnabled, self.captureEnabledSwitch.isChecked())
            cfg.set(cfg.requestTimeout, self.requestTimeoutSpinBox.value())
            
            # 网站设置
            cfg.set(cfg.baseUrl, self.baseUrlEdit.text())
            cfg.set(cfg.loginUrl, self.loginUrlEdit.text())
            cfg.set(cfg.studyUrl, self.studyUrlEdit.text())
            
            # 保存配置
            if save_study_config():
                self.showInfoBar("设置保存成功", "success")
            else:
                self.showInfoBar("设置保存失败", "error")
                
        except Exception as e:
            print(f"[设置] 保存设置失败: {e}")
            self.showInfoBar("保存设置失败", "error")

    def onResetSettings(self):
        """重置设置"""
        # 重新加载默认设置
        self.loadSettings()
        self.showInfoBar("设置已重置", "info")

    def showInfoBar(self, message: str, type: str = "info"):
        """显示信息栏"""
        if type == "success":
            InfoBar.success("", message, duration=3000, parent=self)
        elif type == "warning":
            InfoBar.warning("", message, duration=5000, parent=self)
        elif type == "error":
            InfoBar.error("", message, duration=8000, parent=self)
        else:
            InfoBar.info("", message, duration=3000, parent=self)
