# coding:utf-8
"""
API捕获核心类

该模块实现了API捕获的核心功能，基于Playwright浏览器自动化技术，
监听和捕获指定的API请求和响应数据。

主要功能：
- 监听网络请求
- 捕获目标API响应
- 数据存储和处理
- 错误处理和重试

类说明：
- APICapture: API捕获核心类
"""

import asyncio
import json
import time
from typing import Dict, List, Optional, Callable, Any
from datetime import datetime
from playwright.async_api import Page, Response

from ..common.config_loader import get_study_config
from ..common.constants import TARGET_APIS
from ..database.dao import DAOFactory


class APICapture:
    """
    API捕获核心类
    
    基于Playwright实现的API捕获功能，监听浏览器网络请求，
    捕获指定的API响应数据并存储到数据库。
    """
    
    def __init__(self, page: Optional[Page] = None):
        """
        初始化API捕获器
        
        Args:
            page: Playwright页面对象
        """
        self.page = page
        self.target_apis = TARGET_APIS.copy()
        self.captured_data: Dict[str, Dict] = {}
        self.callbacks: Dict[str, List[Callable]] = {}
        self.is_capturing = False
        self.capture_count = 0
        
        # 配置
        cfg = get_study_config()
        self.enabled = cfg.get(cfg.apiCaptureEnabled)
        self.timeout = cfg.get(cfg.requestTimeout)
        
        # 数据库DAO
        self.api_dao = DAOFactory.get_api_data_dao()
        
        print(f"[API捕获] 初始化完成，目标API数量: {len(self.target_apis)}")
    
    def set_page(self, page: Page):
        """
        设置Playwright页面对象
        
        Args:
            page: Playwright页面对象
        """
        self.page = page
        print("[API捕获] 页面对象已设置")
    
    def add_target_api(self, url: str):
        """
        添加目标API
        
        Args:
            url: API URL
        """
        if url not in self.target_apis:
            self.target_apis.append(url)
            print(f"[API捕获] 添加目标API: {url}")
    
    def remove_target_api(self, url: str):
        """
        移除目标API
        
        Args:
            url: API URL
        """
        if url in self.target_apis:
            self.target_apis.remove(url)
            print(f"[API捕获] 移除目标API: {url}")
    
    def is_target_api(self, url: str) -> bool:
        """
        判断是否为目标API
        
        Args:
            url: 请求URL
            
        Returns:
            bool: 是否为目标API
        """
        return url in self.target_apis
    
    def add_callback(self, api_url: str, callback: Callable):
        """
        添加API响应回调函数
        
        Args:
            api_url: API URL
            callback: 回调函数
        """
        if api_url not in self.callbacks:
            self.callbacks[api_url] = []
        self.callbacks[api_url].append(callback)
        print(f"[API捕获] 为API {api_url} 添加回调函数")
    
    async def start_capture(self):
        """开始API捕获"""
        if not self.enabled:
            print("[API捕获] API捕获功能已禁用")
            return
        
        if not self.page:
            print("[API捕获] 页面对象未设置，无法开始捕获")
            return
        
        if self.is_capturing:
            print("[API捕获] 捕获已在进行中")
            return
        
        try:
            # 设置网络监听器
            self.page.on("response", self._handle_response)
            self.is_capturing = True
            print("[API捕获] 开始监听网络请求")
            
        except Exception as e:
            print(f"[API捕获] 启动捕获失败: {e}")
            self.is_capturing = False
    
    async def stop_capture(self):
        """停止API捕获"""
        if not self.is_capturing:
            print("[API捕获] 捕获未在进行中")
            return
        
        try:
            # 移除网络监听器
            if self.page:
                self.page.remove_listener("response", self._handle_response)
            
            self.is_capturing = False
            print(f"[API捕获] 停止监听，共捕获 {self.capture_count} 个API响应")
            
        except Exception as e:
            print(f"[API捕获] 停止捕获失败: {e}")
    
    async def _handle_response(self, response: Response):
        """
        处理API响应
        
        Args:
            response: Playwright响应对象
        """
        url = response.url
        
        if not self.is_target_api(url):
            return
        
        try:
            print(f"[API捕获] 发现目标API: {url}")
            
            # 检查响应状态
            if response.status != 200:
                print(f"[API捕获] API响应状态异常: {url} (状态: {response.status})")
                await self._save_error_response(url, response.status, "响应状态异常")
                return
            
            # 获取响应数据
            try:
                data = await response.json()
            except Exception as json_error:
                print(f"[API捕获] 解析JSON失败: {url} - {json_error}")
                await self._save_error_response(url, response.status, f"JSON解析失败: {json_error}")
                return
            
            # 存储捕获的数据
            capture_info = {
                'url': url,
                'status': response.status,
                'data': data,
                'timestamp': time.time(),
                'capture_time': datetime.now()
            }
            
            self.captured_data[url] = capture_info
            self.capture_count += 1
            
            print(f"[API捕获] 成功捕获数据: {url}")
            print(f"[API捕获] 数据大小: {len(str(data))} 字符")
            
            # 保存到数据库
            await self._save_to_database(capture_info)
            
            # 触发回调函数
            await self._trigger_callbacks(url, data)
            
            # 输出捕获内容（调试用）
            self._log_captured_data(capture_info)
            
        except Exception as e:
            print(f"[API捕获] 处理API响应失败: {url} - {e}")
            await self._save_error_response(url, response.status if response else 0, str(e))
    
    async def _save_to_database(self, capture_info: Dict[str, Any]):
        """
        保存捕获数据到数据库
        
        Args:
            capture_info: 捕获信息
        """
        try:
            # 创建API数据记录
            api_data = {
                'user_phone': '',  # 需要从当前用户获取
                'api_url': capture_info['url'],
                'request_method': 'GET',  # 默认GET，可以从response获取
                'response_status': capture_info['status'],
                'response_data': json.dumps(capture_info['data'], ensure_ascii=False),
                'capture_time': capture_info['capture_time'],
                'is_processed': False,
                'error_message': None
            }
            
            # 保存到数据库
            result = self.api_dao.create(api_data)
            if result:
                print(f"[API捕获] 数据已保存到数据库: {capture_info['url']}")
            else:
                print(f"[API捕获] 数据库保存失败: {capture_info['url']}")
                
        except Exception as e:
            print(f"[API捕获] 数据库保存异常: {e}")
    
    async def _save_error_response(self, url: str, status: int, error_message: str):
        """
        保存错误响应
        
        Args:
            url: API URL
            status: 响应状态码
            error_message: 错误信息
        """
        try:
            api_data = {
                'user_phone': '',
                'api_url': url,
                'request_method': 'GET',
                'response_status': status,
                'response_data': None,
                'capture_time': datetime.now(),
                'is_processed': False,
                'error_message': error_message
            }
            
            self.api_dao.create(api_data)
            print(f"[API捕获] 错误响应已记录: {url}")
            
        except Exception as e:
            print(f"[API捕获] 错误响应记录失败: {e}")
    
    async def _trigger_callbacks(self, url: str, data: Dict[str, Any]):
        """
        触发回调函数
        
        Args:
            url: API URL
            data: 响应数据
        """
        if url in self.callbacks:
            for callback in self.callbacks[url]:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(data)
                    else:
                        callback(data)
                except Exception as e:
                    print(f"[API捕获] 回调函数执行失败: {e}")
    
    def _log_captured_data(self, capture_info: Dict[str, Any]):
        """
        记录捕获的数据（调试用）
        
        Args:
            capture_info: 捕获信息
        """
        print("=" * 60)
        print(f"[API内容] URL: {capture_info['url']}")
        print(f"[API内容] 状态码: {capture_info['status']}")
        print(f"[API内容] 捕获时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(capture_info['timestamp']))}")
        print("[API内容] 响应数据:")
        
        try:
            formatted_data = json.dumps(capture_info['data'], ensure_ascii=False, indent=2)
            # 如果数据太长，截断显示
            if len(formatted_data) > 2000:
                print(formatted_data[:2000] + "\n... [数据过长，已截断显示] ...")
            else:
                print(formatted_data)
        except Exception as format_error:
            print(f"数据格式化失败: {format_error}")
            print(f"原始数据类型: {type(capture_info['data'])}")
            print(f"原始数据: {str(capture_info['data'])[:500]}...")
        
        print("=" * 60)
    
    def get_captured_data(self, url: Optional[str] = None) -> Dict[str, Any]:
        """
        获取捕获的数据
        
        Args:
            url: 指定URL，如果为None则返回所有数据
            
        Returns:
            Dict[str, Any]: 捕获的数据
        """
        if url:
            return self.captured_data.get(url, {})
        return self.captured_data.copy()
    
    def clear_captured_data(self):
        """清空捕获的数据"""
        self.captured_data.clear()
        self.capture_count = 0
        print("[API捕获] 捕获数据已清空")
    
    def get_capture_stats(self) -> Dict[str, Any]:
        """
        获取捕获统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        return {
            "is_capturing": self.is_capturing,
            "capture_count": self.capture_count,
            "target_apis_count": len(self.target_apis),
            "captured_apis": list(self.captured_data.keys()),
            "enabled": self.enabled
        }
    
    def set_user_phone(self, phone: str):
        """
        设置当前用户手机号（用于数据库记录）
        
        Args:
            phone: 用户手机号
        """
        self.current_user_phone = phone
        print(f"[API捕获] 设置用户手机号: {phone}")
