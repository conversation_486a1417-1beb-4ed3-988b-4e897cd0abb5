# coding:utf-8
"""
设置界面组件

该模块提供系统设置界面，包含各种配置选项的
图形化设置界面。

主要功能：
- 系统配置设置
- 浏览器配置
- OCR配置
- 日志配置
- 并发控制配置

类说明：
- SettingsInterface: 设置界面类
"""

from PySide6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QScrollArea
from PySide6.QtCore import Qt, pyqtSignal
from qfluentwidgets import (ScrollArea, ExpandLayout, SettingCardGroup, SwitchSettingCard,
                           OptionsSettingCard, RangeSettingCard, PushSettingCard,
                           HyperlinkCard, PrimaryPushSettingCard, FluentIcon as FIF,
                           InfoBar, InfoBarPosition, LineEditSettingCard, ComboBoxSettingCard,
                           ColorSettingCard, FolderListSettingCard, Theme, setTheme, isDarkTheme)
from typing import Dict, Any

from ..common.config_loader import study_cfg
from ..logging import log_manager


class SettingsInterface(ScrollArea):
    """
    设置界面类
    
    提供系统配置的图形化设置界面
    """
    
    # 信号定义
    config_changed = pyqtSignal(str, object)  # 配置项名称, 新值
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.cfg = study_cfg
        self.parent_window = parent
        
        self._init_ui()
        self._connect_signals()
    
    def _init_ui(self):
        """初始化界面"""
        # 设置滚动区域
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.setViewportMargins(0, 80, 0, 20)
        self.setWidget(self._create_content_widget())
        self.setWidgetResizable(True)
        
        # 设置样式
        self.setObjectName('settingsInterface')
        self.setStyleSheet("""
            QScrollArea {
                background-color: transparent;
                border: none;
            }
        """)
    
    def _create_content_widget(self) -> QWidget:
        """创建内容组件"""
        content_widget = QWidget()
        content_layout = ExpandLayout(content_widget)
        
        # 系统设置组
        self._create_system_group(content_layout)
        
        # 浏览器设置组
        self._create_browser_group(content_layout)
        
        # OCR设置组
        self._create_ocr_group(content_layout)
        
        # API设置组
        self._create_api_group(content_layout)
        
        # 日志设置组
        self._create_logging_group(content_layout)
        
        # 并发控制设置组
        self._create_concurrency_group(content_layout)
        
        # 个性化设置组
        self._create_personalization_group(content_layout)
        
        # 关于设置组
        self._create_about_group(content_layout)
        
        return content_widget
    
    def _create_system_group(self, layout):
        """创建系统设置组"""
        system_group = SettingCardGroup("系统设置", self)
        
        # 异步登录
        self.async_login_card = SwitchSettingCard(
            FIF.SYNC,
            "异步登录",
            "启用异步登录模式，提高登录效率",
            self.cfg,
            self.cfg.asyncLogin
        )
        system_group.addSettingCard(self.async_login_card)
        
        # 必修课程数量
        self.compulsory_courses_card = RangeSettingCard(
            self.cfg.compulsoryCourses,
            FIF.BOOK_SHELF,
            "必修课程数量",
            "设置需要学习的必修课程数量"
        )
        system_group.addSettingCard(self.compulsory_courses_card)
        
        # 选修课程数量
        self.elective_courses_card = RangeSettingCard(
            self.cfg.electiveCourses,
            FIF.LIBRARY,
            "选修课程数量",
            "设置需要学习的选修课程数量"
        )
        system_group.addSettingCard(self.elective_courses_card)
        
        # 延迟时间
        self.delay_time_card = RangeSettingCard(
            self.cfg.delayTime,
            FIF.TIMER,
            "操作延迟时间",
            "设置自动化操作之间的延迟时间（秒）"
        )
        system_group.addSettingCard(self.delay_time_card)
        
        # 重试次数
        self.retry_count_card = RangeSettingCard(
            self.cfg.retryCount,
            FIF.SYNC,
            "重试次数",
            "设置操作失败时的重试次数"
        )
        system_group.addSettingCard(self.retry_count_card)
        
        layout.addWidget(system_group)
    
    def _create_browser_group(self, layout):
        """创建浏览器设置组"""
        browser_group = SettingCardGroup("浏览器设置", self)
        
        # 浏览器类型
        self.browser_type_card = OptionsSettingCard(
            self.cfg.browserType,
            FIF.GLOBE,
            "浏览器类型",
            "选择自动化使用的浏览器",
            texts=["Chrome", "Firefox", "Edge"]
        )
        browser_group.addSettingCard(self.browser_type_card)
        
        # 无头模式
        self.headless_card = SwitchSettingCard(
            FIF.HIDE,
            "无头模式",
            "启用无头模式，浏览器将在后台运行",
            self.cfg,
            self.cfg.headless
        )
        browser_group.addSettingCard(self.headless_card)
        
        # 禁用图片
        self.disable_images_card = SwitchSettingCard(
            FIF.PHOTO,
            "禁用图片加载",
            "禁用图片加载以提高页面加载速度",
            self.cfg,
            self.cfg.disableImages
        )
        browser_group.addSettingCard(self.disable_images_card)
        
        # 用户代理
        self.user_agent_card = LineEditSettingCard(
            self.cfg.userAgent,
            FIF.DEVELOPER_TOOLS,
            "用户代理",
            "设置浏览器的用户代理字符串"
        )
        browser_group.addSettingCard(self.user_agent_card)
        
        layout.addWidget(browser_group)
    
    def _create_ocr_group(self, layout):
        """创建OCR设置组"""
        ocr_group = SettingCardGroup("OCR设置", self)
        
        # 主要引擎
        self.primary_engine_card = OptionsSettingCard(
            self.cfg.primaryEngine,
            FIF.CAMERA,
            "主要OCR引擎",
            "选择主要使用的OCR识别引擎",
            texts=["Ddddocr", "百度OCR"]
        )
        ocr_group.addSettingCard(self.primary_engine_card)
        
        # 百度API Key
        self.baidu_api_key_card = LineEditSettingCard(
            self.cfg.baiduApiKey,
            FIF.CERTIFICATE,
            "百度API Key",
            "百度OCR服务的API Key"
        )
        ocr_group.addSettingCard(self.baidu_api_key_card)
        
        # 百度Secret Key
        self.baidu_secret_key_card = LineEditSettingCard(
            self.cfg.baiduSecretKey,
            FIF.FINGERPRINT,
            "百度Secret Key",
            "百度OCR服务的Secret Key"
        )
        ocr_group.addSettingCard(self.baidu_secret_key_card)
        
        # OCR超时时间
        self.ocr_timeout_card = RangeSettingCard(
            self.cfg.ocrTimeout,
            FIF.TIMER,
            "OCR超时时间",
            "设置OCR识别的超时时间（秒）"
        )
        ocr_group.addSettingCard(self.ocr_timeout_card)
        
        layout.addWidget(ocr_group)
    
    def _create_api_group(self, layout):
        """创建API设置组"""
        api_group = SettingCardGroup("API设置", self)
        
        # API超时时间
        self.api_timeout_card = RangeSettingCard(
            self.cfg.apiTimeout,
            FIF.TIMER,
            "API超时时间",
            "设置API请求的超时时间（秒）"
        )
        api_group.addSettingCard(self.api_timeout_card)
        
        # API重试次数
        self.api_retry_card = RangeSettingCard(
            self.cfg.apiRetryCount,
            FIF.SYNC,
            "API重试次数",
            "设置API请求失败时的重试次数"
        )
        api_group.addSettingCard(self.api_retry_card)
        
        # API捕获开关
        self.api_capture_card = SwitchSettingCard(
            FIF.CAPTURE,
            "API捕获",
            "启用API请求捕获功能",
            self.cfg,
            self.cfg.apiCaptureEnabled
        )
        api_group.addSettingCard(self.api_capture_card)
        
        # 基础URL
        self.base_url_card = LineEditSettingCard(
            self.cfg.baseUrl,
            FIF.LINK,
            "基础URL",
            "学习平台的基础URL地址"
        )
        api_group.addSettingCard(self.base_url_card)
        
        # 登录URL
        self.login_url_card = LineEditSettingCard(
            self.cfg.loginUrl,
            FIF.SIGNIN,
            "登录URL",
            "学习平台的登录页面URL"
        )
        api_group.addSettingCard(self.login_url_card)
        
        layout.addWidget(api_group)
    
    def _create_logging_group(self, layout):
        """创建日志设置组"""
        logging_group = SettingCardGroup("日志设置", self)
        
        # 日志级别
        self.log_level_card = OptionsSettingCard(
            self.cfg.logLevel,
            FIF.HISTORY,
            "日志级别",
            "设置日志记录的详细程度",
            texts=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        )
        logging_group.addSettingCard(self.log_level_card)
        
        # 控制台日志
        self.console_log_card = SwitchSettingCard(
            FIF.COMMAND_PROMPT,
            "控制台日志",
            "启用控制台日志输出",
            self.cfg,
            self.cfg.enableConsoleLog
        )
        logging_group.addSettingCard(self.console_log_card)
        
        # 文件日志
        self.file_log_card = SwitchSettingCard(
            FIF.DOCUMENT,
            "文件日志",
            "启用文件日志记录",
            self.cfg,
            self.cfg.enableFileLog
        )
        logging_group.addSettingCard(self.file_log_card)
        
        # 数据库日志
        self.database_log_card = SwitchSettingCard(
            FIF.DATABASE,
            "数据库日志",
            "启用数据库日志记录",
            self.cfg,
            self.cfg.enableDatabaseLog
        )
        logging_group.addSettingCard(self.database_log_card)
        
        # 日志保留天数
        self.log_days_card = RangeSettingCard(
            self.cfg.maxDays,
            FIF.CALENDAR,
            "日志保留天数",
            "设置日志文件的保留天数"
        )
        logging_group.addSettingCard(self.log_days_card)
        
        layout.addWidget(logging_group)
    
    def _create_concurrency_group(self, layout):
        """创建并发控制设置组"""
        concurrency_group = SettingCardGroup("并发控制", self)
        
        # 最大并发任务数
        self.max_tasks_card = RangeSettingCard(
            self.cfg.maxConcurrentTasks,
            FIF.PLAY,
            "最大并发任务数",
            "设置同时执行的最大任务数量"
        )
        concurrency_group.addSettingCard(self.max_tasks_card)
        
        # 最大并发用户数
        self.max_users_card = RangeSettingCard(
            self.cfg.maxConcurrentUsers,
            FIF.PEOPLE,
            "最大并发用户数",
            "设置同时学习的最大用户数量"
        )
        concurrency_group.addSettingCard(self.max_users_card)
        
        # 工作线程数
        self.max_workers_card = RangeSettingCard(
            self.cfg.maxWorkers,
            FIF.DEVELOPER_TOOLS,
            "工作线程数",
            "设置线程池的工作线程数量"
        )
        concurrency_group.addSettingCard(self.max_workers_card)
        
        # API速率限制
        self.rate_limit_card = RangeSettingCard(
            self.cfg.apiRateLimit,
            FIF.SPEED_OFF,
            "API速率限制",
            "设置每分钟的API请求限制"
        )
        concurrency_group.addSettingCard(self.rate_limit_card)
        
        layout.addWidget(concurrency_group)
    
    def _create_personalization_group(self, layout):
        """创建个性化设置组"""
        personalization_group = SettingCardGroup("个性化", self)
        
        # 主题设置
        self.theme_card = OptionsSettingCard(
            self.cfg.themeMode,
            FIF.BRUSH,
            "应用主题",
            "选择应用程序的主题模式",
            texts=["浅色", "深色", "跟随系统"]
        )
        personalization_group.addSettingCard(self.theme_card)
        
        # 语言设置
        self.language_card = OptionsSettingCard(
            self.cfg.language if hasattr(self.cfg, 'language') else None,
            FIF.LANGUAGE,
            "界面语言",
            "选择界面显示语言",
            texts=["简体中文", "English"]
        )
        personalization_group.addSettingCard(self.language_card)
        
        layout.addWidget(personalization_group)
    
    def _create_about_group(self, layout):
        """创建关于设置组"""
        about_group = SettingCardGroup("关于", self)
        
        # 检查更新
        self.update_card = PushSettingCard(
            "检查更新",
            FIF.UPDATE,
            "检查更新",
            "检查是否有新版本可用"
        )
        about_group.addSettingCard(self.update_card)
        
        # 重置设置
        self.reset_card = PushSettingCard(
            "重置设置",
            FIF.DELETE,
            "重置所有设置",
            "将所有设置恢复为默认值"
        )
        about_group.addSettingCard(self.reset_card)
        
        # 帮助文档
        self.help_card = HyperlinkCard(
            "https://github.com/your-repo/docs",
            "查看帮助文档",
            FIF.HELP,
            "帮助文档",
            "查看详细的使用说明和帮助文档"
        )
        about_group.addSettingCard(self.help_card)
        
        layout.addWidget(about_group)
    
    def _connect_signals(self):
        """连接信号"""
        # 连接配置变化信号
        self.cfg.configChanged.connect(self._on_config_changed)
        
        # 连接按钮信号
        self.update_card.clicked.connect(self._check_update)
        self.reset_card.clicked.connect(self._reset_settings)
    
    def _on_config_changed(self, key, value):
        """配置变化处理"""
        self.config_changed.emit(key, value)
        log_manager.info(f"配置已更改: {key} = {value}", module="SettingsInterface")
    
    def _check_update(self):
        """检查更新"""
        # TODO: 实现更新检查逻辑
        InfoBar.info(
            title="检查更新",
            content="当前已是最新版本",
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP,
            duration=2000,
            parent=self
        )
    
    def _reset_settings(self):
        """重置设置"""
        # TODO: 实现设置重置逻辑
        InfoBar.warning(
            title="重置设置",
            content="设置已重置为默认值，重启后生效",
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP,
            duration=3000,
            parent=self
        )
    
    def refresh(self):
        """刷新界面"""
        # 重新加载配置
        pass
    
    def save_settings(self):
        """保存设置"""
        try:
            self.cfg.save()
            InfoBar.success(
                title="保存成功",
                content="设置已保存",
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP,
                duration=2000,
                parent=self
            )
        except Exception as e:
            InfoBar.error(
                title="保存失败",
                content=f"保存设置失败: {e}",
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP,
                duration=3000,
                parent=self
            )
