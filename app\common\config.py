# coding:utf-8
"""
应用程序配置模块

该模块定义了应用程序的配置管理系统，包括语言设置、主题配置、
窗口效果等各种应用程序设置项的定义和管理。

主要功能：
- 定义应用程序的各种配置项
- 提供语言枚举和序列化支持
- 管理主题、DPI缩放、云母效果等设置
- 提供系统版本检测功能

类和函数说明：
- Language: 语言枚举类
- LanguageSerializer: 语言序列化器
- Config: 应用程序配置类
- isWin11(): Windows 11 检测函数
"""

import sys
from enum import Enum

from PySide6.QtCore import QLocale
from qfluentwidgets import (qconfig, QConfig, ConfigItem, OptionsConfigItem, BoolValidator,
                            OptionsValidator, RangeConfigItem, RangeValidator,
                            Theme, ConfigSerializer, __version__)


class Language(Enum):
    """
    语言枚举类

    定义应用程序支持的语言选项，包括简体中文、繁体中文、
    英文和自动检测选项。
    """

    CHINESE_SIMPLIFIED = QLocale(QLocale.Chinese, QLocale.China)
    CHINESE_TRADITIONAL = QLocale(QLocale.Chinese, QLocale.HongKong)
    ENGLISH = QLocale(QLocale.English)
    AUTO = QLocale()


class LanguageSerializer(ConfigSerializer):
    """
    语言序列化器

    用于将语言枚举值与字符串之间进行转换，
    支持配置文件的读取和保存。
    """

    def serialize(self, language):
        """将语言枚举序列化为字符串"""
        return language.value.name() if language != Language.AUTO else "Auto"

    def deserialize(self, value: str):
        """将字符串反序列化为语言枚举"""
        return Language(QLocale(value)) if value != "Auto" else Language.AUTO


def isWin11():
    """
    检测是否为Windows 11系统

    Returns:
        bool: 如果是Windows 11系统返回True，否则返回False
    """
    return sys.platform == 'win32' and sys.getwindowsversion().build >= 22000


class Config(QConfig):
    """
    应用程序配置类

    定义应用程序的所有配置项，包括主窗口设置、材质效果、
    软件更新、学习工具等各种配置选项。
    """

    # 主窗口配置
    micaEnabled = ConfigItem("MainWindow", "MicaEnabled", isWin11(), BoolValidator())
    dpiScale = OptionsConfigItem(
        "MainWindow", "DpiScale", "Auto", OptionsValidator([1, 1.25, 1.5, 1.75, 2, "Auto"]), restart=True)
    language = OptionsConfigItem(
        "MainWindow", "Language", Language.AUTO, OptionsValidator(Language), LanguageSerializer(), restart=True)

    # 材质效果配置
    blurRadius  = RangeConfigItem("Material", "AcrylicBlurRadius", 15, RangeValidator(0, 40))

    # 软件更新配置
    checkUpdateAtStartUp = ConfigItem("Update", "CheckUpdateAtStartUp", True, BoolValidator())

    # 学习工具配置
    xueyuanEnabled = ConfigItem("XueYuan", "Enabled", True, BoolValidator())
    xueyuanAutoStart = ConfigItem("XueYuan", "AutoStart", False, BoolValidator())
    xueyuanShowInTray = ConfigItem("XueYuan", "ShowInTray", True, BoolValidator())
    xueyuanMaxConcurrentUsers = RangeConfigItem("XueYuan", "MaxConcurrentUsers", 5, RangeValidator(1, 20))
    xueyuanDefaultBrowser = OptionsConfigItem("XueYuan", "DefaultBrowser", "chrome",
                                             OptionsValidator(["chrome", "firefox", "edge"]))
    xueyuanHeadlessMode = ConfigItem("XueYuan", "HeadlessMode", False, BoolValidator())
    xueyuanAutoRetry = ConfigItem("XueYuan", "AutoRetry", True, BoolValidator())
    xueyuanRetryCount = RangeConfigItem("XueYuan", "RetryCount", 3, RangeValidator(1, 10))
    xueyuanStudyInterval = RangeConfigItem("XueYuan", "StudyInterval", 5, RangeValidator(1, 60))  # 分钟


# 应用程序信息常量
YEAR = 2023
AUTHOR = "zhiyiYo"
VERSION = __version__




# 创建配置实例并加载配置文件
cfg = Config()
cfg.themeMode.value = Theme.AUTO
qconfig.load('app/config/config.json', cfg)

# 导入并初始化学习工具配置
try:
    from ..xueyuan.common.config_loader import init_study_config, get_study_config
    # 初始化学习工具配置
    if cfg.get(cfg.xueyuanEnabled):
        init_study_config()
        # 获取学习工具配置实例
        study_cfg = get_study_config()
        print("[配置] 学习工具配置加载成功")
    else:
        study_cfg = None
        print("[配置] 学习工具已禁用")
except ImportError as e:
    # 如果学习工具模块未安装，创建空的配置对象
    study_cfg = None
    print(f"[配置] 学习工具模块未找到: {e}")
except Exception as e:
    study_cfg = None
    print(f"[配置] 学习工具配置加载失败: {e}")


def get_xueyuan_config():
    """获取学习工具配置实例"""
    return study_cfg


def is_xueyuan_enabled():
    """检查学习工具是否启用"""
    return cfg.get(cfg.xueyuanEnabled) and study_cfg is not None