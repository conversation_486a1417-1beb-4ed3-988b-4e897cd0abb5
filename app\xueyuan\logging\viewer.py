# coding:utf-8
"""
日志查看器

该模块提供日志查看界面，包括日志列表显示、
搜索过滤、实时更新等功能。

主要功能：
- 日志列表显示
- 多条件搜索过滤
- 实时日志更新
- 日志详情查看

类说明：
- LogViewer: 日志查看器界面类
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, 
                               QTableWidget, QTableWidgetItem, QHeaderView,
                               QComboBox, QLineEdit, QPushButton, QDateTimeEdit,
                               QCheckBox, QSplitter, QTextEdit, QLabel,
                               QGroupBox, QGridLayout, QSpinBox)
from PySide6.QtCore import Qt, QTimer, Signal, QDateTime, QThread, pyqtSignal
from PySide6.QtGui import QFont, QColor
from qfluentwidgets import (FluentIcon as FIF, PushButton, ComboBox, LineEdit,
                           TableWidget, TextEdit, CheckBox, SpinBox,
                           DateTimeEdit, GroupBox, ScrollArea)
from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta

from .manager import log_manager


class LogUpdateThread(QThread):
    """日志更新线程"""
    
    logs_updated = pyqtSignal(list)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.running = False
        self.filters = {}
        self.update_interval = 2  # 秒
    
    def set_filters(self, filters: Dict[str, Any]):
        """设置过滤条件"""
        self.filters = filters
    
    def run(self):
        """线程运行"""
        self.running = True
        
        while self.running:
            try:
                # 获取日志
                logs = log_manager.get_logs(
                    level=self.filters.get('level'),
                    user_phone=self.filters.get('user_phone'),
                    module=self.filters.get('module'),
                    start_time=self.filters.get('start_time'),
                    end_time=self.filters.get('end_time'),
                    limit=self.filters.get('limit', 1000),
                    source=self.filters.get('source', 'cache')
                )
                
                self.logs_updated.emit(logs)
                
            except Exception as e:
                print(f"[日志查看] 更新日志失败: {e}")
            
            # 等待
            self.msleep(self.update_interval * 1000)
    
    def stop(self):
        """停止线程"""
        self.running = False
        self.quit()
        self.wait()


class LogViewer(QWidget):
    """
    日志查看器界面类
    
    提供日志查看和搜索功能
    """
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_logs: List[Dict[str, Any]] = []
        self.selected_log: Optional[Dict[str, Any]] = None
        
        # 更新线程
        self.update_thread = LogUpdateThread(self)
        self.update_thread.logs_updated.connect(self._on_logs_updated)
        
        self._init_ui()
        self._connect_signals()
        
        # 初始加载
        self._load_logs()
    
    def _init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("日志查看器")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #333;")
        layout.addWidget(title_label)
        
        # 过滤器区域
        filter_group = self._create_filter_group()
        layout.addWidget(filter_group)
        
        # 分割器
        splitter = QSplitter(Qt.Horizontal)
        layout.addWidget(splitter)
        
        # 日志列表
        self.log_table = self._create_log_table()
        splitter.addWidget(self.log_table)
        
        # 日志详情
        detail_widget = self._create_detail_widget()
        splitter.addWidget(detail_widget)
        
        # 设置分割器比例
        splitter.setSizes([600, 400])
        
        # 控制按钮
        button_layout = self._create_button_layout()
        layout.addLayout(button_layout)
    
    def _create_filter_group(self) -> GroupBox:
        """创建过滤器组"""
        group = GroupBox("过滤条件")
        layout = QGridLayout(group)
        
        # 日志级别
        layout.addWidget(QLabel("日志级别:"), 0, 0)
        self.level_combo = ComboBox()
        self.level_combo.addItems(["全部", "DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"])
        self.level_combo.setCurrentText("全部")
        layout.addWidget(self.level_combo, 0, 1)
        
        # 用户手机号
        layout.addWidget(QLabel("用户手机号:"), 0, 2)
        self.user_phone_edit = LineEdit()
        self.user_phone_edit.setPlaceholderText("输入用户手机号")
        layout.addWidget(self.user_phone_edit, 0, 3)
        
        # 模块名称
        layout.addWidget(QLabel("模块名称:"), 1, 0)
        self.module_edit = LineEdit()
        self.module_edit.setPlaceholderText("输入模块名称")
        layout.addWidget(self.module_edit, 1, 1)
        
        # 关键词搜索
        layout.addWidget(QLabel("关键词:"), 1, 2)
        self.keyword_edit = LineEdit()
        self.keyword_edit.setPlaceholderText("输入搜索关键词")
        layout.addWidget(self.keyword_edit, 1, 3)
        
        # 时间范围
        layout.addWidget(QLabel("开始时间:"), 2, 0)
        self.start_time_edit = DateTimeEdit()
        self.start_time_edit.setDateTime(QDateTime.currentDateTime().addDays(-1))
        layout.addWidget(self.start_time_edit, 2, 1)
        
        layout.addWidget(QLabel("结束时间:"), 2, 2)
        self.end_time_edit = DateTimeEdit()
        self.end_time_edit.setDateTime(QDateTime.currentDateTime())
        layout.addWidget(self.end_time_edit, 2, 3)
        
        # 数据源和限制
        layout.addWidget(QLabel("数据源:"), 3, 0)
        self.source_combo = ComboBox()
        self.source_combo.addItems(["缓存", "数据库"])
        self.source_combo.setCurrentText("缓存")
        layout.addWidget(self.source_combo, 3, 1)
        
        layout.addWidget(QLabel("显示数量:"), 3, 2)
        self.limit_spin = SpinBox()
        self.limit_spin.setRange(10, 10000)
        self.limit_spin.setValue(1000)
        layout.addWidget(self.limit_spin, 3, 3)
        
        # 实时更新
        self.auto_update_check = CheckBox("实时更新")
        self.auto_update_check.setChecked(True)
        layout.addWidget(self.auto_update_check, 4, 0)
        
        return group
    
    def _create_log_table(self) -> TableWidget:
        """创建日志表格"""
        table = TableWidget()
        
        # 设置列
        headers = ["时间", "级别", "用户", "模块", "消息"]
        table.setColumnCount(len(headers))
        table.setHorizontalHeaderLabels(headers)
        
        # 设置列宽
        header = table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # 时间
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # 级别
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # 用户
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # 模块
        header.setSectionResizeMode(4, QHeaderView.Stretch)  # 消息
        
        # 设置选择模式
        table.setSelectionBehavior(QTableWidget.SelectRows)
        table.setSelectionMode(QTableWidget.SingleSelection)
        
        # 设置样式
        table.setAlternatingRowColors(True)
        
        return table
    
    def _create_detail_widget(self) -> QWidget:
        """创建详情窗口"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 标题
        detail_label = QLabel("日志详情")
        detail_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #333;")
        layout.addWidget(detail_label)
        
        # 详情文本
        self.detail_text = TextEdit()
        self.detail_text.setReadOnly(True)
        self.detail_text.setPlaceholderText("选择日志记录查看详情")
        layout.addWidget(self.detail_text)
        
        return widget
    
    def _create_button_layout(self) -> QHBoxLayout:
        """创建按钮布局"""
        layout = QHBoxLayout()
        
        # 搜索按钮
        self.search_btn = PushButton("搜索", self, FIF.SEARCH)
        layout.addWidget(self.search_btn)
        
        # 刷新按钮
        self.refresh_btn = PushButton("刷新", self, FIF.SYNC)
        layout.addWidget(self.refresh_btn)
        
        # 清空按钮
        self.clear_btn = PushButton("清空", self, FIF.DELETE)
        layout.addWidget(self.clear_btn)
        
        # 导出按钮
        self.export_btn = PushButton("导出", self, FIF.SAVE)
        layout.addWidget(self.export_btn)
        
        layout.addStretch()
        
        # 统计信息
        self.stats_label = QLabel("总计: 0 条")
        self.stats_label.setStyleSheet("color: #666;")
        layout.addWidget(self.stats_label)
        
        return layout
    
    def _connect_signals(self):
        """连接信号"""
        # 过滤器变化
        self.level_combo.currentTextChanged.connect(self._on_filter_changed)
        self.user_phone_edit.textChanged.connect(self._on_filter_changed)
        self.module_edit.textChanged.connect(self._on_filter_changed)
        self.keyword_edit.textChanged.connect(self._on_filter_changed)
        self.source_combo.currentTextChanged.connect(self._on_filter_changed)
        
        # 按钮点击
        self.search_btn.clicked.connect(self._load_logs)
        self.refresh_btn.clicked.connect(self._load_logs)
        self.clear_btn.clicked.connect(self._clear_logs)
        self.export_btn.clicked.connect(self._export_logs)
        
        # 表格选择
        self.log_table.itemSelectionChanged.connect(self._on_selection_changed)
        
        # 实时更新
        self.auto_update_check.toggled.connect(self._on_auto_update_toggled)
    
    def _on_filter_changed(self):
        """过滤器变化处理"""
        if self.auto_update_check.isChecked():
            self._update_thread_filters()
    
    def _on_auto_update_toggled(self, checked: bool):
        """实时更新切换处理"""
        if checked:
            self._start_auto_update()
        else:
            self._stop_auto_update()
    
    def _start_auto_update(self):
        """启动自动更新"""
        if not self.update_thread.isRunning():
            self._update_thread_filters()
            self.update_thread.start()
    
    def _stop_auto_update(self):
        """停止自动更新"""
        if self.update_thread.isRunning():
            self.update_thread.stop()
    
    def _update_thread_filters(self):
        """更新线程过滤条件"""
        filters = self._get_current_filters()
        self.update_thread.set_filters(filters)
    
    def _get_current_filters(self) -> Dict[str, Any]:
        """获取当前过滤条件"""
        filters = {}
        
        # 日志级别
        level = self.level_combo.currentText()
        if level != "全部":
            filters['level'] = level
        
        # 用户手机号
        user_phone = self.user_phone_edit.text().strip()
        if user_phone:
            filters['user_phone'] = user_phone
        
        # 模块名称
        module = self.module_edit.text().strip()
        if module:
            filters['module'] = module
        
        # 时间范围
        start_time = self.start_time_edit.dateTime().toPython()
        end_time = self.end_time_edit.dateTime().toPython()
        filters['start_time'] = start_time
        filters['end_time'] = end_time
        
        # 数据源
        source = "cache" if self.source_combo.currentText() == "缓存" else "database"
        filters['source'] = source
        
        # 显示数量
        filters['limit'] = self.limit_spin.value()
        
        return filters
    
    def _load_logs(self):
        """加载日志"""
        try:
            filters = self._get_current_filters()
            
            # 获取日志
            logs = log_manager.get_logs(
                level=filters.get('level'),
                user_phone=filters.get('user_phone'),
                module=filters.get('module'),
                start_time=filters.get('start_time'),
                end_time=filters.get('end_time'),
                limit=filters.get('limit', 1000),
                source=filters.get('source', 'cache')
            )
            
            # 关键词过滤
            keyword = self.keyword_edit.text().strip()
            if keyword:
                logs = [
                    log for log in logs
                    if keyword.lower() in log.get('message', '').lower()
                ]
            
            self._update_log_table(logs)
            
        except Exception as e:
            print(f"[日志查看] 加载日志失败: {e}")
    
    def _on_logs_updated(self, logs: List[Dict[str, Any]]):
        """日志更新处理"""
        # 关键词过滤
        keyword = self.keyword_edit.text().strip()
        if keyword:
            logs = [
                log for log in logs
                if keyword.lower() in log.get('message', '').lower()
            ]
        
        self._update_log_table(logs)
    
    def _update_log_table(self, logs: List[Dict[str, Any]]):
        """更新日志表格"""
        self.current_logs = logs
        
        # 清空表格
        self.log_table.setRowCount(0)
        
        # 添加日志行
        for i, log in enumerate(logs):
            self.log_table.insertRow(i)
            
            # 时间
            timestamp = log.get('timestamp', datetime.now())
            time_str = timestamp.strftime('%Y-%m-%d %H:%M:%S') if isinstance(timestamp, datetime) else str(timestamp)
            self.log_table.setItem(i, 0, QTableWidgetItem(time_str))
            
            # 级别
            level = log.get('level', 'UNKNOWN')
            level_item = QTableWidgetItem(level)
            
            # 设置级别颜色
            if level == 'ERROR' or level == 'CRITICAL':
                level_item.setForeground(QColor('#FF4444'))
            elif level == 'WARNING':
                level_item.setForeground(QColor('#FF8800'))
            elif level == 'INFO':
                level_item.setForeground(QColor('#0066CC'))
            elif level == 'DEBUG':
                level_item.setForeground(QColor('#666666'))
            
            self.log_table.setItem(i, 1, level_item)
            
            # 用户
            user_phone = log.get('user_phone', 'unknown')
            self.log_table.setItem(i, 2, QTableWidgetItem(user_phone))
            
            # 模块
            module = log.get('module', 'unknown')
            self.log_table.setItem(i, 3, QTableWidgetItem(module))
            
            # 消息
            message = log.get('message', '')
            self.log_table.setItem(i, 4, QTableWidgetItem(message))
        
        # 更新统计
        self.stats_label.setText(f"总计: {len(logs)} 条")
    
    def _on_selection_changed(self):
        """选择变化处理"""
        current_row = self.log_table.currentRow()
        if 0 <= current_row < len(self.current_logs):
            self.selected_log = self.current_logs[current_row]
            self._update_detail_view()
        else:
            self.selected_log = None
            self.detail_text.clear()
    
    def _update_detail_view(self):
        """更新详情视图"""
        if not self.selected_log:
            return
        
        # 格式化详情信息
        details = []
        details.append(f"时间: {self.selected_log.get('timestamp', 'N/A')}")
        details.append(f"级别: {self.selected_log.get('level', 'N/A')}")
        details.append(f"用户: {self.selected_log.get('user_phone', 'N/A')}")
        details.append(f"模块: {self.selected_log.get('module', 'N/A')}")
        details.append(f"消息: {self.selected_log.get('message', 'N/A')}")
        
        # 添加额外字段
        for key, value in self.selected_log.items():
            if key not in ['timestamp', 'level', 'user_phone', 'module', 'message']:
                details.append(f"{key}: {value}")
        
        self.detail_text.setPlainText('\n'.join(details))
    
    def _clear_logs(self):
        """清空日志"""
        try:
            # 获取清理条件
            filters = self._get_current_filters()
            
            # 清理日志
            log_manager.clear_logs(
                older_than=filters.get('end_time'),
                level=filters.get('level')
            )
            
            # 刷新显示
            self._load_logs()
            
            print("[日志查看] 日志已清空")
            
        except Exception as e:
            print(f"[日志查看] 清空日志失败: {e}")
    
    def _export_logs(self):
        """导出日志"""
        try:
            from .exporter import LogExporter
            
            exporter = LogExporter()
            filters = self._get_current_filters()
            
            # 导出当前显示的日志
            success = exporter.export_logs(
                logs=self.current_logs,
                format='excel',
                filename=f"logs_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            )
            
            if success:
                print("[日志查看] 日志导出成功")
            else:
                print("[日志查看] 日志导出失败")
                
        except Exception as e:
            print(f"[日志查看] 导出日志失败: {e}")
    
    def closeEvent(self, event):
        """关闭事件"""
        self._stop_auto_update()
        super().closeEvent(event)
